version: '3.8'

# Production-specific Docker Compose configuration
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --profile production

services:
  jarvis-app:
    environment:
      # Production optimizations
      - NODE_OPTIONS=--max-old-space-size=2048
      - LOG_LEVEL=info
      - TRUST_PROXY=true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  nginx:
    environment:
      - NGINX_WORKER_PROCESSES=auto
      - NGINX_WORKER_CONNECTIONS=1024
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

# Production-ready network configuration
networks:
  default:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
