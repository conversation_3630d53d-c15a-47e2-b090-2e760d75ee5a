# Frontend Environment Variables
# Copy this file to .env.local and update with your values

# Backend API Configuration
VITE_API_BASE_URL=https://jarvis-frontend-v2-production.up.railway.app/api
VITE_REALTIME_API_BASE_URL=https://jarvis-frontend-v2-production.up.railway.app/api

# Optional: OpenAI API Key for direct frontend calls (if needed)
# VITE_OPENAI_API_KEY=your_openai_api_key_here

# OAuth Configuration (for backend)
# Generate these from OAuth providers:
# Google: https://console.developers.google.com/
# GitHub: https://github.com/settings/developers
# Microsoft: https://portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationsListBlade

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# GitHub OAuth
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Microsoft OAuth
MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret

# Session & JWT Configuration
SESSION_SECRET=your_session_secret_key
JWT_SECRET=your_jwt_secret_key
