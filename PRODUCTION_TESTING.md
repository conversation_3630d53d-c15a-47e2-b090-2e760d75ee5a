# Production Testing Checklist - JARVIS Application

## Pre-Production Testing Guide

### **Phase 1: Local Environment Testing** ✅

#### **1.1 Service Health Checks**
```bash
# Backend Health Check
curl http://localhost:5050/api/health

# Frontend Health Check
curl http://localhost:5173

# Database Check
curl http://localhost:5050/api/users/me -H "Authorization: Bearer YOUR_TOKEN"
```

#### **1.2 Authentication Flow Testing**
- [ ] Mock authentication endpoints respond correctly
- [ ] JWT tokens are generated and validated
- [ ] User data is stored in database
- [ ] Session management works properly
- [ ] Logout functionality clears tokens

#### **1.3 API Endpoints Testing**
```bash
# Test all backend endpoints

# Authentication
GET  http://localhost:5050/auth/user
POST http://localhost:5050/auth/logout

# Mock auth (for testing)
POST http://localhost:5050/auth/mock/login

# User endpoints
GET  http://localhost:5050/api/users/me
PUT  http://localhost:5050/api/users/preferences

# Thread management (if OpenAI configured)
POST http://localhost:5050/api/thread
POST http://localhost:5050/api/message
```

#### **1.4 Frontend Functionality**
- [ ] Login page loads correctly
- [ ] Mock login buttons work
- [ ] User profile displays after login
- [ ] Logout returns to login screen
- [ ] Responsive design works on mobile/desktop

### **Phase 2: Environment Configuration Testing**

#### **2.1 Environment Variables**
```bash
# Backend (.env)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret
SESSION_SECRET=your_session_secret
JWT_SECRET=your_jwt_secret
OPENAI_API_KEY=your_openai_key
OPENAI_ASSISTANT_ID=your_assistant_id

# Frontend (.env.local)
VITE_API_BASE_URL=https://your-backend-url.com/api
VITE_REALTIME_API_BASE_URL=https://your-backend-url.com/api
```

#### **2.2 CORS Configuration**
- [ ] Local development CORS works (localhost:5173 ↔ localhost:5050)
- [ ] Production domains added to CORS whitelist
- [ ] Railway/Netlify domains configured

### **Phase 3: Integration Testing**

#### **3.1 End-to-End Flow Testing**
1. **User Registration Flow**
   - Navigate to frontend
   - Click OAuth provider
   - Complete OAuth flow
   - Verify user created in database
   - Check JWT token received

2. **Session Management**
   - Refresh page - user should remain logged in
   - Logout - should return to login screen
   - Login again - should work seamlessly

3. **User Preferences**
   - Update user preferences
   - Verify changes saved to database
   - Check preferences persist across sessions

#### **3.2 Error Handling Testing**
- [ ] Invalid JWT tokens are rejected
- [ ] Expired tokens redirect to login
- [ ] Missing environment variables handled gracefully
- [ ] Database connection failures handled
- [ ] OAuth callback failures handled

### **Phase 4: Performance & Security Testing**

#### **4.1 Security Checks**
- [ ] HTTPS enforced in production
- [ ] JWT tokens use secure secrets
- [ ] Session cookies are httpOnly and secure
- [ ] Input validation on all endpoints
- [ ] Rate limiting on auth endpoints
- [ ] CORS properly configured

#### **4.2 Performance Testing**
- [ ] Database queries are optimized
- [ ] JWT token validation is fast
- [ ] OAuth callbacks complete quickly
- [ ] Frontend loads within 3 seconds
- [ ] API responses under 500ms

### **Phase 5: Production Readiness**

#### **5.1 Database Migration**
- [ ] SQLite → PostgreSQL migration script ready
- [ ] Database connection pooling configured
- [ ] Backup strategy implemented
- [ ] Migration rollback plan prepared

#### **5.2 Deployment Configuration**
- [ ] Railway deployment configured
- [ ] Netlify deployment configured
- [ ] Environment variables set in production
- [ ] Health check endpoints working
- [ ] Logging and monitoring setup

### **Phase 6: Real OAuth Testing**

#### **6.1 Google OAuth**
- [ ] Create Google Cloud project
- [ ] Configure OAuth credentials
- [ ] Add redirect URIs (localhost + production)
- [ ] Test complete Google login flow

#### **6.2 GitHub OAuth**
- [ ] Create GitHub OAuth app
- [ ] Configure OAuth credentials
- [ ] Add callback URLs
- [ ] Test complete GitHub login flow

#### **6.3 Microsoft OAuth**
- [ ] Create Azure AD app registration
- [ ] Configure OAuth credentials
- [ ] Add redirect URIs
- [] Test complete Microsoft login flow

## **Automated Testing Script**

Save this as `test-production.sh`:

```bash
#!/bin/bash

echo "🧪 Starting Production Testing..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test functions
test_endpoint() {
    local method=$1
    local url=$2
    local description=$3
    
    echo -n "Testing $description... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    else
        response=$(curl -s -o /dev/null -w "%{http_code}" -X "$method" "$url")
    fi
    
    if [ "$response" = "200" ] || [ "$response" = "201" ] || [ "$response" = "302" ]; then
        echo -e "${GREEN}✅ PASS${NC}"
    else
        echo -e "${RED}❌ FAIL (HTTP $response)${NC}"
    fi
}

# Backend tests
echo "🔍 Testing Backend..."
test_endpoint "GET" "http://localhost:5050/api/health" "Backend Health"
test_endpoint "GET" "http://localhost:5050/auth/user" "Auth User Endpoint"
test_endpoint "POST" "http://localhost:5050/auth/mock/login" "Mock Auth Login"

# Frontend tests
echo "🔍 Testing Frontend..."
test_endpoint "GET" "http://localhost:5173" "Frontend Load"

# Database tests
echo "🔍 Testing Database..."
if [ -f "backend/database.sqlite" ]; then
    echo -e "${GREEN}✅ Database file exists${NC}"
else
    echo -e "${RED}❌ Database file missing${NC}"
fi

# Environment checks
echo "🔍 Checking Environment..."
if [ -f "backend/.env.example" ]; then
    echo -e "${GREEN}✅ Environment template exists${NC}"
else
    echo -e "${RED}❌ Environment template missing${NC}"
fi

echo "🎉 Production testing complete!"
```

## **Quick Validation Commands**

### **Run the testing script:**
```bash
chmod +x test-production.sh
./test-production.sh
```

### **Manual API testing:**
```bash
# Test mock login
curl -X POST http://localhost:5050/auth/mock/login \
  -H "Content-Type: application/json" \
  -d '{"provider": "google"}'

# Test user endpoint (after login)
curl http://localhost:5050/auth/user \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Database verification:**
```bash
# Check database content
sqlite3 backend/database.sqlite "SELECT * FROM users;"
```

## **Production Checklist Summary**

| Component | Status | Notes |
|-----------|--------|--------|
| Backend API | ✅ | Running on port 5050 |
| Frontend | ✅ | Running on port 5173 |
| Database | ✅ | SQLite initialized |
| Mock Auth | ✅ | All providers tested |
| CORS | ✅ | Configured for local dev |
| Environment | ✅ | Templates ready |
| Real OAuth | ⏳ | Ready for credentials |
| Deployment | ⏳ | Ready for Railway/Netlify |

**Next Steps:**
1. Run the testing script to validate current setup
2. Add real OAuth credentials
3. Test with real providers
4. Deploy to production
5. Monitor and validate production deployment
