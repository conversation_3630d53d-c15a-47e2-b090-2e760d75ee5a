# JARVIS Docker Containerization Guide

This guide covers the complete containerization setup for the JARVIS AI Assistant, including local development and production deployment.

## Overview

The JARVIS application uses a multi-stage Docker build approach with separate configurations for development and production environments. This unified approach eliminates the complexity of managing separate frontend and backend deployments.

## Architecture

```
┌─────────────────────────────────────────┐
│           Docker Architecture           │
├─────────────────────────────────────────┤
│  Development Environment                │
│  ┌─────────────────┐ ┌─────────────────┐│
│  │   Frontend      │ │    Backend      ││
│  │   (Vite:5173)   │ │  (Express:5050) ││
│  │   Hot Reload    │ │   No<PERSON>mon       ││
│  └─────────────────┘ └─────────────────┘│
├─────────────────────────────────────────┤
│  Production Environment                 │
│  ┌─────────────────────────────────────┐│
│  │        Unified App                  ││
│  │    Frontend + Backend (Port 5050)  ││
│  │         + Nginx (Optional)          ││
│  └─────────────────────────────────────┘│
└─────────────────────────────────────────┘
```

## Quick Start

### Prerequisites

- Docker and Docker Compose installed
- Node.js 20+ (for local development without Docker)
- Your OpenAI API key

### 1. Environment Setup

```bash
# Copy environment template
cp .env.docker .env

# Edit with your API keys
nano .env
```

Required environment variables:
```env
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ASSISTANT_ID=your_assistant_id_here
```

### 2. Development Environment

```bash
# Start development environment
./docker-setup.sh dev

# Or manually:
docker-compose --profile development up -d jarvis-dev
```

Access points:
- **Frontend**: http://localhost:5173 (Vite dev server with hot reload)
- **Backend API**: http://localhost:5050
- **Health Check**: http://localhost:5050/health

### 3. Production Environment

```bash
# Start production environment
./docker-setup.sh prod

# Or manually:
docker-compose --profile production up -d jarvis-app
```

Access points:
- **Application**: http://localhost:5050 (unified frontend + backend)
- **Health Check**: http://localhost:5050/health

## Docker Setup Script

The `docker-setup.sh` script provides convenient commands for managing containers:

```bash
# Development
./docker-setup.sh dev          # Start development environment
./docker-setup.sh build-dev    # Build development containers

# Production
./docker-setup.sh prod         # Start production environment
./docker-setup.sh build        # Build production containers

# Management
./docker-setup.sh stop         # Stop all containers
./docker-setup.sh logs         # View logs
./docker-setup.sh cleanup      # Clean up resources
./docker-setup.sh help         # Show help
```

## Docker Compose Profiles

The setup uses Docker Compose profiles to manage different environments:

### Development Profile
- **Service**: `jarvis-dev`
- **Ports**: 5173 (frontend), 5050 (backend)
- **Features**: Hot reload, volume mounting, debug mode
- **Database**: Optional PostgreSQL for development

### Production Profile
- **Service**: `jarvis-app`
- **Port**: 5050 (unified application)
- **Features**: Optimized build, security hardening, health checks
- **Proxy**: Optional Nginx for SSL termination

## File Structure

```
├── Dockerfile                 # Multi-stage build configuration
├── docker-compose.yml         # Main compose configuration
├── docker-compose.dev.yml     # Development overrides
├── docker-compose.prod.yml    # Production overrides
├── docker-setup.sh           # Management script
├── .dockerignore             # Docker ignore patterns
├── .env.docker               # Environment template
└── nginx.conf                # Nginx configuration
```

## Environment Variables

### Required
- `OPENAI_API_KEY`: Your OpenAI API key
- `OPENAI_ASSISTANT_ID`: Your OpenAI Assistant ID

### Optional
- `N8N_BASE_URL`: n8n workflow automation URL
- `N8N_API_KEY`: n8n API key for workflow integration
- `WEATHER_API_KEY`: Weather service API key
- `CORS_ORIGIN`: CORS origin (auto-configured for each environment)

## Local Testing

### 1. Test Development Environment

```bash
# Start development containers
./docker-setup.sh dev

# Check container status
docker-compose ps

# View logs
docker-compose logs -f jarvis-dev

# Test endpoints
curl http://localhost:5050/health
curl http://localhost:5173  # Should serve frontend
```

### 2. Test Production Environment

```bash
# Stop development first
./docker-setup.sh stop

# Start production
./docker-setup.sh prod

# Test unified application
curl http://localhost:5050/health
curl http://localhost:5050  # Should serve built frontend
```

### 3. Test API Functionality

```bash
# Test OpenAI integration (requires valid API key)
curl -X POST http://localhost:5050/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello JARVIS"}'

# Test file upload
curl -X POST http://localhost:5050/api/upload \
  -F "file=@test-file.txt"
```

## Deployment Options

### 1. Single Server Deployment (Recommended)

Deploy the production container to any Docker-compatible hosting:

```bash
# Build for deployment
docker build --target production -t jarvis-app .

# Run on server
docker run -d \
  --name jarvis-production \
  -p 5050:5050 \
  --env-file .env \
  -v $(pwd)/uploads:/app/uploads \
  jarvis-app
```

### 2. Docker Swarm

```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.yml -c docker-compose.prod.yml jarvis
```

### 3. Kubernetes

Use the provided Kubernetes manifests (if available) or convert Docker Compose:

```bash
# Convert to Kubernetes (requires kompose)
kompose convert -f docker-compose.yml -f docker-compose.prod.yml
```

## Troubleshooting

### Common Issues

1. **Port conflicts**:
   ```bash
   # Check what's using ports
   lsof -i :5050
   lsof -i :5173
   
   # Stop conflicting processes or change ports in docker-compose.yml
   ```

2. **Environment variables not loaded**:
   ```bash
   # Verify .env file exists and has correct format
   cat .env
   
   # Restart containers after env changes
   ./docker-setup.sh stop
   ./docker-setup.sh dev  # or prod
   ```

3. **Build failures**:
   ```bash
   # Clean build cache
   docker builder prune
   
   # Rebuild without cache
   docker-compose build --no-cache
   ```

4. **Permission issues**:
   ```bash
   # Fix uploads directory permissions
   sudo chown -R $(id -u):$(id -g) uploads/
   ```

### Debug Commands

```bash
# Enter running container
docker-compose exec jarvis-dev sh  # or jarvis-app

# View detailed logs
docker-compose logs --tail=100 -f jarvis-dev

# Check container resources
docker stats

# Inspect container configuration
docker inspect jarvis-frontend_jarvis-dev_1
```

## Performance Optimization

### Development
- Volume mounting for hot reload
- Separate frontend/backend processes
- Debug logging enabled

### Production
- Multi-stage build for smaller images
- Non-root user for security
- Resource limits and health checks
- Optimized Node.js settings

## Security Considerations

1. **Non-root user**: Production containers run as `appuser`
2. **Environment variables**: Sensitive data in `.env` file (not committed)
3. **Network isolation**: Custom Docker network
4. **Health checks**: Automatic container health monitoring
5. **Resource limits**: Memory and CPU constraints in production

## Monitoring and Logging

### Log Management
```bash
# View logs with timestamps
docker-compose logs -t jarvis-app

# Follow logs in real-time
docker-compose logs -f --tail=50 jarvis-app

# Export logs
docker-compose logs jarvis-app > jarvis.log
```

### Health Monitoring
- Built-in health checks on `/health` endpoint
- Container restart policies
- Resource usage monitoring with `docker stats`

## Next Steps

1. **Test locally**: Use `./docker-setup.sh dev` to test development environment
2. **Configure environment**: Update `.env` with your API keys
3. **Deploy**: Choose deployment method (single server, cloud provider, etc.)
4. **Monitor**: Set up logging and monitoring for production
5. **Scale**: Consider load balancing for high-traffic scenarios

For deployment to cloud providers like Hetzner, DigitalOcean, or AWS, see the deployment-specific guides in the `memory-bank/` directory.
