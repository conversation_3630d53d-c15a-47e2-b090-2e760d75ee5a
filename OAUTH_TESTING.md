# OAuth Testing Guide - JARVIS Frontend

## Overview
OAuth authentication has been implemented with Google, GitHub, and Microsoft providers. The system uses JWT tokens for stateless authentication and SQLite for user storage in development.

## Current Status ✅
- [x] Backend OAuth packages installed
- [x] User database schema created (SQLite)
- [x] Passport.js strategies configured (optional when env vars missing)
- [x] Authentication endpoints created
- [x] JWT token generation implemented
- [x] Frontend AuthContext created
- [x] Login component with provider buttons
- [x] UserProfile component for logged-in users
- [x] Frontend-backend connection ready

## Testing Steps

### 1. Environment Setup
```bash
# Backend (in /backend directory)
# Create .env file with OAuth credentials:
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret
SESSION_SECRET=your_session_secret
JWT_SECRET=your_jwt_secret
OPENAI_API_KEY=your_openai_key
OPENAI_ASSISTANT_ID=your_assistant_id

# Frontend (in root directory)
# Update .env.local:
VITE_API_BASE_URL=http://localhost:5050/api
VITE_REALTIME_API_BASE_URL=http://localhost:5050/api
```

### 2. Start Services
```bash
# Terminal 1: Start backend
cd backend
npm install
node server.js

# Terminal 2: Start frontend
cd ../
npm run dev
```

### 3. OAuth Provider Configuration

#### Google OAuth
1. Go to https://console.developers.google.com/
2. Create new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:5050/auth/google/callback` (development)
   - `https://your-backend-url.com/auth/google/callback` (production)

#### GitHub OAuth
1. Go to https://github.com/settings/developers
2. Create new OAuth App
3. Set Authorization callback URL:
   - `http://localhost:5050/auth/github/callback` (development)
   - `https://your-backend-url.com/auth/github/callback` (production)

#### Microsoft OAuth
1. Go to https://portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationsListBlade
2. Register new application
3. Add redirect URIs:
   - `http://localhost:5050/auth/microsoft/callback` (development)
   - `https://your-backend-url.com/auth/microsoft/callback` (production)

### 4. Testing Authentication Flow

#### Manual Testing
1. Navigate to `http://localhost:5173`
2. Click "Sign in with Google/GitHub/Microsoft"
3. Complete OAuth flow
4. Verify user profile appears in top-right
5. Test logout functionality

#### API Testing
```bash
# Test authentication endpoints
curl http://localhost:5050/auth/google
curl http://localhost:5050/auth/user (with JWT token)
curl http://localhost:5050/auth/logout
```

### 5. Frontend Integration Testing

#### AuthContext Testing
```javascript
// In browser console when logged in
import { useAuth } from './src/contexts/AuthContext.jsx'
const { user, isAuthenticated, login, logout } = useAuth()
console.log('User:', user)
console.log('Is authenticated:', isAuthenticated)
```

#### Protected Route Testing
1. Access app without authentication → should show login screen
2. Login → should redirect to main app
3. Refresh page → should maintain session
4. Logout → should return to login screen

## Troubleshooting

### Common Issues

#### CORS Errors
- Backend CORS is configured for localhost:5173 and production domains
- Check `server.js` CORS configuration if issues persist

#### OAuth Callback Issues
- Ensure redirect URLs match exactly in OAuth provider settings
- Check that backend URL is accessible from OAuth provider
- Verify environment variables are set correctly

#### Database Issues
- SQLite database will be created as `database.sqlite` in backend folder
- User table schema is auto-created on server startup
- Check console for database initialization messages

#### Missing Environment Variables
- Server will start without OAuth if credentials are missing
- Check console output for missing variable warnings
- All OAuth providers are optional - app works without them

### Debug Mode
```bash
# Enable debug logging
DEBUG=passport:* node server.js
```

## Production Deployment

### Environment Variables for Production
```bash
# Backend (Railway/Render/Heroku)
GOOGLE_CLIENT_ID=production_google_client_id
GOOGLE_CLIENT_SECRET=production_google_client_secret
GITHUB_CLIENT_ID=production_github_client_id
GITHUB_CLIENT_SECRET=production_github_client_secret
MICROSOFT_CLIENT_ID=production_microsoft_client_id
MICROSOFT_CLIENT_SECRET=production_microsoft_client_secret
SESSION_SECRET=secure_random_string
JWT_SECRET=secure_random_string
DATABASE_URL=postgresql://... (for production)

# Frontend (Netlify/Vercel)
VITE_API_BASE_URL=https://your-backend-url.com/api
VITE_REALTIME_API_BASE_URL=https://your-backend-url.com/api
```

### Database Migration
For production, update `config/database.js` to use PostgreSQL:
```javascript
// Replace SQLite with PostgreSQL pool
import pg from 'pg';
const pool = new pg.Pool({ connectionString: process.env.DATABASE_URL });
```

## Security Notes
- JWT tokens expire in 24 hours
- Session cookies are httpOnly and secure in production
- OAuth state parameter is used for CSRF protection
- User data is sanitized before storage

## Next Steps
1. Configure OAuth providers with your credentials
2. Test the authentication flow
3. Deploy to production with proper environment variables
4. Monitor for any CORS or callback issues
5. Consider adding rate limiting for auth endpoints
