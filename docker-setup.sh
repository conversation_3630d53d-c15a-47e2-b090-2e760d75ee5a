#!/bin/bash

# JARVIS Docker Setup Script
# This script helps set up and manage Docker containers for local development and production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is installed and running
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi

    print_success "Docker is installed and running"
}

# Function to check if required files exist
check_files() {
    local required_files=("Dockerfile" "docker-compose.yml" "package.json" "backend/package.json")
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            print_error "Required file $file not found"
            exit 1
        fi
    done
    
    print_success "All required files found"
}

# Function to create .env file if it doesn't exist
setup_env() {
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.docker" ]]; then
            print_status "Copying .env.docker to .env"
            cp .env.docker .env
        elif [[ -f ".env.example" ]]; then
            print_status "Copying .env.example to .env"
            cp .env.example .env
        else
            print_warning "No .env file found. Creating basic .env file."
            cat > .env << EOF
# JARVIS Environment Variables
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ASSISTANT_ID=your_assistant_id_here
NODE_ENV=development
PORT=5050
CORS_ORIGIN=http://localhost:5173
N8N_BASE_URL=https://n8n.scrapha.com
N8N_API_KEY=your_n8n_api_key_here
WEATHER_API_KEY=your_weather_api_key_here
EOF
        fi
        
        print_warning "Please edit .env file with your actual API keys before running containers"
    else
        print_success ".env file already exists"
    fi
}

# Function to build containers
build_containers() {
    local target=${1:-production}
    
    print_status "Building Docker containers for $target environment..."
    
    if [[ "$target" == "development" ]]; then
        docker-compose build jarvis-dev
    else
        docker-compose build jarvis-app
    fi
    
    print_success "Containers built successfully"
}

# Function to start development environment
start_dev() {
    print_status "Starting development environment..."
    
    # Stop any running containers first
    docker-compose --profile development down
    
    # Start development containers
    docker-compose --profile development up -d jarvis-dev
    
    print_success "Development environment started!"
    print_status "Frontend available at: http://localhost:5173"
    print_status "Backend API available at: http://localhost:5050"
    print_status "View logs with: docker-compose logs -f jarvis-dev"
}

# Function to start production environment
start_prod() {
    print_status "Starting production environment..."
    
    # Stop any running containers first
    docker-compose --profile production down
    
    # Start production containers
    docker-compose --profile production up -d jarvis-app
    
    print_success "Production environment started!"
    print_status "Application available at: http://localhost:5050"
    print_status "View logs with: docker-compose logs -f jarvis-app"
}

# Function to stop all containers
stop_all() {
    print_status "Stopping all containers..."
    docker-compose --profile development --profile production down
    print_success "All containers stopped"
}

# Function to view logs
view_logs() {
    local service=${1:-jarvis-app}
    print_status "Viewing logs for $service..."
    docker-compose logs -f "$service"
}

# Function to clean up Docker resources
cleanup() {
    print_status "Cleaning up Docker resources..."
    
    # Stop all containers
    docker-compose --profile development --profile production down
    
    # Remove unused containers, networks, images
    docker system prune -f
    
    # Remove project-specific volumes (optional)
    read -p "Remove project volumes? This will delete database data (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose down -v
        print_warning "Project volumes removed"
    fi
    
    print_success "Cleanup completed"
}

# Function to show help
show_help() {
    echo "JARVIS Docker Setup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev         Start development environment"
    echo "  prod        Start production environment"
    echo "  build       Build containers (default: production)"
    echo "  build-dev   Build development containers"
    echo "  stop        Stop all containers"
    echo "  logs        View application logs"
    echo "  cleanup     Clean up Docker resources"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev              # Start development environment"
    echo "  $0 prod             # Start production environment"
    echo "  $0 logs jarvis-dev  # View development logs"
    echo "  $0 cleanup          # Clean up resources"
}

# Main script logic
main() {
    case "${1:-help}" in
        "dev")
            check_docker
            check_files
            setup_env
            build_containers "development"
            start_dev
            ;;
        "prod")
            check_docker
            check_files
            setup_env
            build_containers "production"
            start_prod
            ;;
        "build")
            check_docker
            check_files
            build_containers "production"
            ;;
        "build-dev")
            check_docker
            check_files
            build_containers "development"
            ;;
        "stop")
            stop_all
            ;;
        "logs")
            view_logs "${2:-jarvis-app}"
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
