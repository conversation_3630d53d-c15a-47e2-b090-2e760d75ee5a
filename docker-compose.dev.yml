version: '3.8'

# Development-specific Docker Compose configuration
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --profile development

services:
  jarvis-dev:
    environment:
      # Override for development
      - DEBUG=true
      - LOG_LEVEL=debug
      - HOT_RELOAD=true
    volumes:
      # Additional development volumes
      - ./.vscode:/app/.vscode:ro
      - ./memory-bank:/app/memory-bank
    stdin_open: true
    tty: true

  # Development database with persistent data
  postgres-dev:
    environment:
      - POSTGRES_DB=jarvis_dev
      - POSTGRES_USER=jarvis
      - POSTGRES_PASSWORD=jarvis_dev_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/migrations:/docker-entrypoint-initdb.d:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U jarvis -d jarvis_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_dev_data:
    driver: local
