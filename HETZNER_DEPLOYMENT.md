# JARVIS AI Assistant - Hetzner Deployment Guide

## Overview
This guide covers deploying the JARVIS AI Assistant as a single Docker container on Hetzner Cloud, eliminating the complexity of separate frontend/backend deployments.

## Prerequisites
- Hetzner Cloud account
- Domain name (optional, for SSL)
- OpenAI API key and Assistant ID
- SSH key for server access

## Server Setup

### 1. Create Hetzner Cloud Server
```bash
# Recommended specs for JARVIS AI Assistant
- CPU: 2 vCPUs (CX21 or better)
- RAM: 4GB minimum
- Storage: 40GB SSD
- OS: Ubuntu 22.04 LTS
```

### 2. Initial Server Configuration
```bash
# Connect to your server
ssh root@your-server-ip

# Update system
apt update && apt upgrade -y

# Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
apt install docker-compose-plugin -y

# Create application directory
mkdir -p /opt/jarvis
cd /opt/jarvis
```

### 3. Deploy Application
```bash
# Clone your repository
git clone https://github.com/paccloud/Jarvis-frontend-v2.git .

# Copy environment template
cp .env.docker .env

# Edit environment variables
nano .env
```

### 4. Configure Environment Variables
Edit `.env` file with your actual values:
```bash
# OpenAI Configuration
OPENAI_API_KEY=sk-proj-your-actual-key-here
OPENAI_ASSISTANT_ID=asst_your-assistant-id-here

# Server Configuration
NODE_ENV=production
PORT=5050
CORS_ORIGIN=http://your-domain.com

# n8n Integration (Optional)
N8N_BASE_URL=https://n8n.scrapha.com
N8N_API_KEY=your-n8n-api-key-here
```

### 5. Build and Start Application
```bash
# Build and start the application
docker compose up -d

# Check logs
docker compose logs -f jarvis-app

# Verify health
curl http://localhost:5050/health
```

## Domain and SSL Setup (Optional)

### 1. Point Domain to Server
- Create A record: `your-domain.com` → `your-server-ip`
- Wait for DNS propagation (5-30 minutes)

### 2. SSL with Let's Encrypt
```bash
# Install certbot
apt install certbot -y

# Get SSL certificate
certbot certonly --standalone -d your-domain.com

# Copy certificates for nginx
mkdir -p ssl
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/cert.pem
cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/key.pem

# Update nginx.conf to enable HTTPS
nano nginx.conf

# Restart with nginx proxy
docker compose --profile production up -d
```

## Monitoring and Maintenance

### 1. Health Checks
```bash
# Check application health
curl http://your-domain.com/health

# Check container status
docker compose ps

# View logs
docker compose logs jarvis-app
```

### 2. Updates
```bash
# Pull latest code
git pull origin main

# Rebuild and restart
docker compose down
docker compose up -d --build
```

### 3. Backup
```bash
# Backup uploads directory
tar -czf jarvis-backup-$(date +%Y%m%d).tar.gz uploads/

# Backup environment file
cp .env .env.backup
```

## Firewall Configuration
```bash
# Allow SSH, HTTP, and HTTPS
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable
```

## Troubleshooting

### Common Issues
1. **Container won't start**: Check logs with `docker compose logs jarvis-app`
2. **API errors**: Verify OpenAI API key and Assistant ID in `.env`
3. **Frontend not loading**: Check if build completed successfully
4. **CORS errors**: Update `CORS_ORIGIN` in `.env` to match your domain

### Useful Commands
```bash
# Restart application
docker compose restart jarvis-app

# View real-time logs
docker compose logs -f jarvis-app

# Execute commands in container
docker compose exec jarvis-app bash

# Check disk usage
df -h
docker system df
```

## Cost Estimation
- Hetzner CX21 server: ~€4.90/month
- Domain (optional): ~€10-15/year
- Total: ~€5-6/month

## Security Considerations
- Keep server updated: `apt update && apt upgrade`
- Use strong SSH keys
- Enable firewall (ufw)
- Regular backups
- Monitor logs for suspicious activity
- Use HTTPS in production

## Support
- Hetzner Cloud Console: https://console.hetzner.cloud/
- Docker logs: `docker compose logs jarvis-app`
- Application health: `http://your-domain.com/health`
