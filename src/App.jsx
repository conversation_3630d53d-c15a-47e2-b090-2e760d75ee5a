import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext.jsx';
import JarvisAgent from './components/JarvisAgent.jsx';
import JarvisRealtimeAgent from './components/JarvisRealtimeAgent.jsx';
import Login from './components/Login.jsx';
import UserProfile from './components/UserProfile.jsx';
import { ToggleLeft, ToggleRight } from 'lucide-react';

function AuthCallbackHandler() {
  const { handleAuthCallback } = useAuth();
  const [processed, setProcessed] = useState(false);

  useEffect(() => {
    const handled = handleAuthCallback();
    if (handled) {
      setProcessed(true);
      // Redirect to home after processing
      window.location.replace('/');
    } else {
      // No auth data, redirect to login
      window.location.replace('/');
    }
  }, [handleAuthCallback]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="text-white">Processing authentication...</div>
    </div>
  );
}

function MainApp() {
  const [useRealtime, setUseRealtime] = useState(false);
  const { user, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return <Login />;
  }

  return (
    <div className="relative">
      {/* User Profile and Mode Toggle */}
      <div className="absolute top-4 right-4 z-50 flex items-center space-x-4">
        <div className="bg-gray-800/90 backdrop-blur-sm rounded-lg p-3 border border-gray-700">
          <div className="flex items-center gap-3">
            <span className={`text-sm ${!useRealtime ? 'text-white' : 'text-gray-400'}`}>
              Assistant API
            </span>
            <button
              onClick={() => setUseRealtime(!useRealtime)}
              className="flex items-center"
            >
              {useRealtime ? (
                <ToggleRight size={24} className="text-green-400" />
              ) : (
                <ToggleLeft size={24} className="text-gray-400" />
              )}
            </button>
            <span className={`text-sm ${useRealtime ? 'text-white' : 'text-gray-400'}`}>
              Realtime API
            </span>
          </div>
          <div className="text-xs text-gray-500 mt-1 text-center">
            {useRealtime ? 'Real-time voice conversation' : 'Traditional chat interface'}
          </div>
        </div>
        <UserProfile />
      </div>

      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {useRealtime ? <JarvisRealtimeAgent /> : <JarvisAgent />}
      </div>
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          <Route path="/auth/callback" element={<AuthCallbackHandler />} />
          <Route path="/" element={<MainApp />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
