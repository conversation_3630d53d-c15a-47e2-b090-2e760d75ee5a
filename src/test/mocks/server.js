import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'

// Mock API handlers
export const handlers = [
  // Mock backend health check
  http.get('http://localhost:5050/health', () => {
    return HttpResponse.json({ status: 'ok', timestamp: new Date().toISOString() })
  }),

  // Mock OpenAI Assistant API endpoints
  http.post('http://localhost:5050/api/assistant/message', async ({ request }) => {
    const body = await request.json()
    return HttpResponse.json({
      success: true,
      response: `Mock response to: ${body.message}`,
      threadId: 'mock-thread-id'
    })
  }),

  http.post('http://localhost:5050/api/assistant/thread', () => {
    return HttpResponse.json({
      success: true,
      threadId: 'mock-thread-id'
    })
  }),

  // Mock realtime session endpoint
  http.post('http://localhost:5050/api/realtime/session', () => {
    return HttpResponse.json({
      success: true,
      sessionToken: 'mock-session-token'
    })
  }),

  // Mock file upload endpoint
  http.post('http://localhost:5050/api/upload', () => {
    return HttpResponse.json({
      success: true,
      fileId: 'mock-file-id',
      message: 'File uploaded successfully'
    })
  }),

  // Mock agent routing endpoint
  http.post('http://localhost:5050/api/agent/route', async ({ request }) => {
    const body = await request.json()
    return HttpResponse.json({
      success: true,
      agent: 'general',
      confidence: 0.8,
      query: body.query
    })
  })
]

// Setup server with handlers
export const server = setupServer(...handlers)
