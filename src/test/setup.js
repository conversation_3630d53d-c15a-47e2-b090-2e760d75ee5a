import '@testing-library/jest-dom'
import { beforeEach, afterEach } from 'vitest'
import { cleanup } from '@testing-library/react'

// Mock Web Speech API
global.webkitSpeechRecognition = class MockSpeechRecognition {
  constructor() {
    this.continuous = false
    this.interimResults = false
    this.lang = 'en-US'
    this.onresult = null
    this.onerror = null
    this.onend = null
    this.onstart = null
  }
  
  start() {
    if (this.onstart) this.onstart()
  }
  
  stop() {
    if (this.onend) this.onend()
  }
  
  abort() {
    if (this.onend) this.onend()
  }
}

global.speechSynthesis = {
  speak: vi.fn(),
  cancel: vi.fn(),
  pause: vi.fn(),
  resume: vi.fn(),
  getVoices: vi.fn(() => []),
  speaking: false,
  pending: false,
  paused: false
}

global.SpeechSynthesisUtterance = class MockSpeechSynthesisUtterance {
  constructor(text) {
    this.text = text
    this.voice = null
    this.volume = 1
    this.rate = 1
    this.pitch = 1
    this.onstart = null
    this.onend = null
    this.onerror = null
    this.onpause = null
    this.onresume = null
    this.onmark = null
    this.onboundary = null
  }
}

// Mock File API
global.File = class MockFile {
  constructor(bits, name, options = {}) {
    this.bits = bits
    this.name = name
    this.type = options.type || ''
    this.size = bits.reduce((acc, bit) => acc + bit.length, 0)
    this.lastModified = options.lastModified || Date.now()
  }
}

global.FileReader = class MockFileReader {
  constructor() {
    this.result = null
    this.error = null
    this.readyState = 0
    this.onload = null
    this.onerror = null
    this.onabort = null
    this.onloadstart = null
    this.onloadend = null
    this.onprogress = null
  }
  
  readAsDataURL(file) {
    setTimeout(() => {
      this.result = `data:${file.type};base64,mock-base64-data`
      this.readyState = 2
      if (this.onload) this.onload({ target: this })
    }, 0)
  }
  
  readAsText(file) {
    setTimeout(() => {
      this.result = 'mock file content'
      this.readyState = 2
      if (this.onload) this.onload({ target: this })
    }, 0)
  }
}

// Mock fetch globally
global.fetch = vi.fn()

// Clean up after each test case
afterEach(() => {
  cleanup()
  vi.clearAllMocks()
})

// Reset fetch mock before each test
beforeEach(() => {
  fetch.mockClear()
})
