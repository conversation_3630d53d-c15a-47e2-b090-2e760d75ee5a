import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import JarvisAgent from '../JarvisAgent'

// Mock the fetch function
global.fetch = vi.fn()

describe('JarvisAgent Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Mock successful backend health check
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ status: 'ok' })
    })
  })

  it('renders the main interface correctly', async () => {
    render(<JarvisAgent />)
    
    // Check for main UI elements
    expect(screen.getByPlaceholderText(/Type your message or use the mic/i)).toBeInTheDocument()
    const buttons = screen.getAllByRole('button')
    expect(buttons.length).toBeGreaterThan(0)
    expect(screen.getByText(/Backend Status/i)).toBeInTheDocument()
  })

  it('displays backend connection status', async () => {
    render(<JarvisAgent />)
    
    await waitFor(() => {
      expect(screen.getByText(/Backend Connected/i)).toBeInTheDocument()
    })
  })

  it('handles text input and message sending', async () => {
    const user = userEvent.setup()
    
    // Mock successful message response
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        response: 'Test response from JARVIS',
        threadId: 'test-thread-id'
      })
    })

    render(<JarvisAgent />)
    
    const input = screen.getByPlaceholderText(/Type your message or use the mic/i)
    const buttons = screen.getAllByRole('button')
    const sendButton = buttons.find(button => button.type === 'submit')
    
    // Type a message
    await user.type(input, 'Hello JARVIS')
    expect(input).toHaveValue('Hello JARVIS')
    
    // Send the message
    await user.click(sendButton)
    
    // Verify API call was made
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/assistant/message'),
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('Hello JARVIS')
        })
      )
    })
  })

  it('handles voice recognition toggle', async () => {
    const user = userEvent.setup()
    render(<JarvisAgent />)
    
    // Find the mic button (it's the first button, which is the large circular button)
    const buttons = screen.getAllByRole('button')
    const voiceButton = buttons[0] // The mic button is the first button
    
    await user.click(voiceButton)
    
    // Should update placeholder text to show listening state
    expect(screen.getByPlaceholderText(/Listening/i)).toBeInTheDocument()
  })

  it('handles file upload', async () => {
    const user = userEvent.setup()
    
    render(<JarvisAgent />)
    
    // Check that file upload component is rendered
    const fileUploadArea = screen.getByRole('presentation')
    expect(fileUploadArea).toBeInTheDocument()
    
    // File upload functionality is handled by FileUpload component
    // This test verifies the component is rendered and integrated
  })

  it('displays error messages when backend is unavailable', async () => {
    // Mock failed backend connection
    fetch.mockRejectedValueOnce(new Error('Network error'))
    
    render(<JarvisAgent />)
    
    await waitFor(() => {
      expect(screen.getByText(/Backend Disconnected/i)).toBeInTheDocument()
    })
  })

  it('handles message history correctly', async () => {
    const user = userEvent.setup()
    
    // Mock successful message response
    fetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        response: 'Test response',
        threadId: 'test-thread-id'
      })
    })

    render(<JarvisAgent />)
    
    const input = screen.getByPlaceholderText(/Type your message or use the mic/i)
    const buttons = screen.getAllByRole('button')
    const sendButton = buttons.find(button => button.type === 'submit')
    
    // Send first message
    await user.type(input, 'First message')
    await user.click(sendButton)
    
    // Wait for response
    await waitFor(() => {
      expect(screen.getByText('First message')).toBeInTheDocument()
      expect(screen.getByText('Test response')).toBeInTheDocument()
    })
    
    // Send second message
    await user.clear(input)
    await user.type(input, 'Second message')
    await user.click(sendButton)
    
    // Verify both messages are in history
    await waitFor(() => {
      expect(screen.getByText('First message')).toBeInTheDocument()
      expect(screen.getByText('Second message')).toBeInTheDocument()
    })
  })
})
