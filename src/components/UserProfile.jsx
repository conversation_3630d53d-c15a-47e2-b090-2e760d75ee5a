import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const UserProfile = () => {
  const { user, logout } = useAuth();

  if (!user) return null;

  return (
    <div className="flex items-center space-x-4">
      {user.avatarUrl && (
        <img
          src={user.avatarUrl}
          alt={user.name}
          className="w-10 h-10 rounded-full border-2 border-purple-500"
        />
      )}
      <div className="hidden md:block">
        <p className="text-sm font-medium text-white">{user.name}</p>
        <p className="text-xs text-gray-300">{user.email}</p>
      </div>
      <button
        onClick={logout}
        className="px-3 py-1 text-sm text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
      >
        Logout
      </button>
    </div>
  );
};

export default UserProfile;
