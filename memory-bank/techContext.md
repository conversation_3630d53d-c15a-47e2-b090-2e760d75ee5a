# Technology Context

## Full-Stack Architecture
- **Frontend**: React + Vite (Build tool)
- **Backend**: Node.js + Express
- **Database**: Not directly used (OpenAI Assistants API)
- **AI Integration**: OpenAI Assistants API + Realtime API
- **Deployment**: Netlify (frontend) + Railway (backend)

## Frontend Stack
### Framework & Runtime
- **Vite**: Fast build tool and dev server
- **React 18**: Core UI library
- **JavaScript (ES6+)**: Primary programming language
- **Tailwind CSS**: Utility-first CSS framework for styling

### Key Frontend Dependencies
- `react`: Core React library
- `react-dom`: React DOM rendering
- `lucide-react`: Icon library for UI components
- `react-dropzone`: File upload functionality

### Browser APIs Used
- **Web Speech API**: 
  - `webkitSpeechRecognition`: Speech-to-text functionality
  - `speechSynthesis`: Text-to-speech functionality
- **Fetch API**: HTTP requests to backend API
- **DOM APIs**: Standard web APIs for UI interaction
- **File API**: File upload and processing

## Backend & Database
- **Node.js/Express**: Standalone server handling OpenAI Assistant API calls
- **No direct database**: Uses OpenAI Assistants API for persistence

### Key Backend Dependencies
- `openai`: Official OpenAI API client
- `express`: Web framework
- `cors`: Cross-origin resource sharing
- `helmet`: Security middleware
- `express-rate-limit`: API rate limiting
- `dotenv`: Environment variable management
- `axios`: HTTP client for external APIs

## AI Integration
- **OpenAI Assistants API**: GPT-4 powered conversational AI
- **OpenAI Realtime API**: Voice-to-voice conversation capabilities
- **Function Calling**: Tool integration for weather, email, workflows
- **Thread Management**: Persistent conversation contexts
- **Tool System**: Extensible function definitions for external integrations

## Available Tools/Functions
1. **Weather Information**: Location-based weather data
2. **Email Sending**: SMTP integration capabilities
3. **n8n Workflow Triggers**: Automation workflow integration
4. **File Processing**: Document analysis and categorization
5. **Web Search**: Information retrieval capabilities

## API Architecture
- **RESTful Design**: Standard HTTP methods and status codes
- **JSON Communication**: Request/response format
- **Thread-based Sessions**: Persistent conversation state
- **Error Handling**: Comprehensive error responses
- **Real-time Communication**: WebSocket for voice features

## Security Features
- **Rate Limiting**: API abuse prevention
- **CORS Configuration**: Cross-origin request management
- **Helmet Security**: HTTP header security
- **Environment Variables**: Secure API key management
- **Input Validation**: Request sanitization

## Development & Deployment
- **Package Manager**: pnpm (Standardized for monorepo)
- **Build Tool**: Vite (frontend) + Node.js (backend)
- **Development Server**: Vite dev server (frontend)
- **Frontend Deployment**: Netlify
- **Backend Deployment**: Railway
- **Live URL**: https://jarvis-frontend.windsurf.build
