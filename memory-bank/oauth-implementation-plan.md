# JARVIS AI Assistant - OAuth Authentication Implementation Plan

## Project Status Assessment

### ✅ Hetzner Deployment Readiness
- **Docker Configuration**: Multi-stage build with proper health checks
- **Container Orchestration**: docker-compose.yml with nginx reverse proxy
- **Environment Management**: Comprehensive .env configuration
- **Deployment Documentation**: Complete Hetzner deployment guide
- **Security**: Basic rate limiting and CORS configured

### ❌ Authentication Gaps
- No user authentication system
- All API endpoints publicly accessible
- No user session management
- No persistent user data storage
- Frontend lacks authentication UI

## OAuth Authentication Architecture

### System Overview
```
Frontend (React) → OAuth Providers → Backend (Node.js) → Database (PostgreSQL)
                                  ↓
                              JWT Tokens → Protected API Routes
```

### OAuth Providers Integration
- **Google OAuth 2.0**: Primary provider for Gmail users
- **GitHub OAuth**: Developer-focused authentication
- **Microsoft OAuth**: Enterprise and Office 365 users

### Database Schema Design

#### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    avatar_url TEXT,
    provider VARCHAR(50) NOT NULL, -- 'google', 'github', 'microsoft'
    provider_id VARCHAR(255) NOT NULL,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(provider, provider_id)
);
```

#### Conversations Table
```sql
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    openai_thread_id VARCHAR(255) UNIQUE,
    agent_type VARCHAR(50) DEFAULT 'general',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Messages Table
```sql
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);
```

## Implementation Roadmap

### Phase 1: Backend Authentication Infrastructure
1. **Database Setup**
   - Install PostgreSQL dependencies (pg, prisma)
   - Create database schema and migrations
   - Set up connection pooling

2. **OAuth Configuration**
   - Install passport.js with OAuth strategies
   - Configure Google, GitHub, Microsoft providers
   - Set up OAuth callback routes

3. **JWT Token Management**
   - Implement token generation and validation
   - Create refresh token mechanism
   - Add token blacklisting for logout

4. **Session Management**
   - Configure express-session with PostgreSQL store
   - Implement secure cookie handling
   - Add CSRF protection

### Phase 2: API Protection and User Management
1. **Authentication Middleware**
   - Create JWT verification middleware
   - Implement role-based access control
   - Add rate limiting per user

2. **User API Routes**
   - User profile management
   - Preferences CRUD operations
   - Account deletion and data export

3. **Conversation Persistence**
   - Link OpenAI threads to user accounts
   - Implement conversation history storage
   - Add conversation search and filtering

### Phase 3: Frontend Authentication Integration
1. **Authentication Context**
   - React context for auth state
   - Token storage and refresh logic
   - Automatic logout on token expiry

2. **Authentication UI Components**
   - OAuth provider selection
   - Login/logout buttons
   - User profile dropdown

3. **Protected Routes**
   - Route guards for authenticated users
   - Redirect logic for unauthenticated access
   - Loading states during auth checks

4. **User Dashboard**
   - Conversation history browser
   - User preferences panel
   - Account management interface

### Phase 4: Production Deployment Enhancements
1. **Docker Configuration Updates**
   - Add PostgreSQL service to docker-compose
   - Configure persistent volumes
   - Update health checks

2. **Hetzner Deployment Modifications**
   - Database initialization scripts
   - Environment variable management
   - SSL certificate handling for OAuth redirects

3. **Security Hardening**
   - HTTPS enforcement
   - Secure headers configuration
   - Input validation and sanitization

## Required Dependencies

### Backend Dependencies
```json
{
  "passport": "^0.7.0",
  "passport-google-oauth20": "^2.0.0",
  "passport-github2": "^0.1.12",
  "passport-microsoft": "^1.0.0",
  "pg": "^8.11.3",
  "prisma": "^5.7.1",
  "@prisma/client": "^5.7.1",
  "express-session": "^1.17.3",
  "connect-pg-simple": "^9.0.1",
  "jsonwebtoken": "^9.0.2",
  "bcryptjs": "^2.4.3",
  "express-rate-limit": "^7.1.5",
  "express-validator": "^7.0.1"
}
```

### Frontend Dependencies
```json
{
  "@tanstack/react-query": "^5.17.0",
  "react-router-dom": "^6.20.1",
  "zustand": "^4.4.7",
  "axios": "^1.6.2"
}
```

## Environment Variables Configuration

### OAuth Provider Setup
```bash
# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=https://your-domain.com/auth/google/callback

# GitHub OAuth
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_CALLBACK_URL=https://your-domain.com/auth/github/callback

# Microsoft OAuth
MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret
MICROSOFT_CALLBACK_URL=https://your-domain.com/auth/microsoft/callback

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/jarvis_db

# Security
SESSION_SECRET=your_session_secret_key
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_EXPIRES_IN=30d

# Application
FRONTEND_URL=https://your-frontend-domain.com
BACKEND_URL=https://your-backend-domain.com
```

## Security Considerations

### Authentication Security
- Secure OAuth redirect URI validation
- PKCE (Proof Key for Code Exchange) for OAuth flows
- JWT token rotation and blacklisting
- Rate limiting on authentication endpoints

### Data Protection
- Encrypted password storage (for fallback auth)
- Secure session cookie configuration
- HTTPS enforcement in production
- Input validation and sanitization

### Privacy Compliance
- User data export functionality
- Account deletion with data purging
- Minimal data collection principle
- Clear privacy policy integration

## Testing Strategy

### Unit Tests
- OAuth provider integration tests
- JWT token validation tests
- Database model tests
- API endpoint tests

### Integration Tests
- Complete OAuth flow testing
- Database transaction tests
- Frontend-backend integration tests

### Security Tests
- Authentication bypass attempts
- Token manipulation tests
- CSRF protection validation
- Rate limiting verification

## Monitoring and Logging

### Authentication Metrics
- Login success/failure rates
- OAuth provider usage statistics
- Token refresh frequency
- Session duration analytics

### Security Monitoring
- Failed authentication attempts
- Suspicious activity detection
- Rate limit violations
- Token abuse patterns

## Backup and Recovery

### Database Backup Strategy
- Automated daily backups
- Point-in-time recovery capability
- Cross-region backup replication
- Backup integrity verification

### Disaster Recovery
- Database failover procedures
- OAuth provider outage handling
- Session recovery mechanisms
- Data migration procedures

## Performance Optimization

### Database Optimization
- Connection pooling configuration
- Query optimization and indexing
- Caching strategy for user sessions
- Database monitoring and alerting

### Frontend Performance
- Token refresh optimization
- Lazy loading of authenticated components
- Efficient state management
- Bundle size optimization

This implementation plan provides a comprehensive roadmap for adding robust OAuth authentication to the JARVIS AI Assistant while maintaining Hetzner deployment readiness.