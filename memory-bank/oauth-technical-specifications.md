# JARVIS AI Assistant - OAuth Technical Specifications

## Authentication Flow Architecture

### OAuth 2.0 Authorization Code Flow
```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant OAuth Provider
    participant Database

    User->>Frontend: Click "Login with Google/GitHub/Microsoft"
    Frontend->>Backend: GET /auth/{provider}
    Backend->>OAuth Provider: Redirect to authorization URL
    OAuth Provider->>User: Show consent screen
    User->>OAuth Provider: Grant permission
    OAuth Provider->>Backend: Callback with authorization code
    Backend->>OAuth Provider: Exchange code for access token
    OAuth Provider->>Backend: Return access token + user info
    Backend->>Database: Create/update user record
    Backend->>Backend: Generate JWT token
    Backend->>Frontend: Redirect with JWT token
    Frontend->>Frontend: Store token and update auth state
```

## Backend Implementation Details

### 1. Passport.js Configuration

#### OAuth Strategies Setup
```javascript
// config/passport.js
import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { Strategy as GitHubStrategy } from 'passport-github2';
import { Strategy as MicrosoftStrategy } from 'passport-microsoft';
import { User } from '../models/User.js';

// Google OAuth Strategy
passport.use(new GoogleStrategy({
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: process.env.GOOGLE_CALLBACK_URL
}, async (accessToken, refreshToken, profile, done) => {
    try {
        const user = await User.findOrCreate({
            provider: 'google',
            providerId: profile.id,
            email: profile.emails[0].value,
            name: profile.displayName,
            avatarUrl: profile.photos[0].value
        });
        return done(null, user);
    } catch (error) {
        return done(error, null);
    }
}));

// GitHub OAuth Strategy
passport.use(new GitHubStrategy({
    clientID: process.env.GITHUB_CLIENT_ID,
    clientSecret: process.env.GITHUB_CLIENT_SECRET,
    callbackURL: process.env.GITHUB_CALLBACK_URL
}, async (accessToken, refreshToken, profile, done) => {
    try {
        const user = await User.findOrCreate({
            provider: 'github',
            providerId: profile.id,
            email: profile.emails[0].value,
            name: profile.displayName || profile.username,
            avatarUrl: profile.photos[0].value
        });
        return done(null, user);
    } catch (error) {
        return done(error, null);
    }
}));

// Microsoft OAuth Strategy
passport.use(new MicrosoftStrategy({
    clientID: process.env.MICROSOFT_CLIENT_ID,
    clientSecret: process.env.MICROSOFT_CLIENT_SECRET,
    callbackURL: process.env.MICROSOFT_CALLBACK_URL,
    scope: ['user.read']
}, async (accessToken, refreshToken, profile, done) => {
    try {
        const user = await User.findOrCreate({
            provider: 'microsoft',
            providerId: profile.id,
            email: profile.emails[0].value,
            name: profile.displayName,
            avatarUrl: profile.photos[0].value
        });
        return done(null, user);
    } catch (error) {
        return done(error, null);
    }
}));
```

### 2. Authentication Routes

#### OAuth Routes Implementation
```javascript
// routes/auth.js
import express from 'express';
import passport from 'passport';
import jwt from 'jsonwebtoken';
import { User } from '../models/User.js';

const router = express.Router();

// Google OAuth routes
router.get('/google', 
    passport.authenticate('google', { scope: ['profile', 'email'] })
);

router.get('/google/callback',
    passport.authenticate('google', { session: false }),
    async (req, res) => {
        const token = jwt.sign(
            { userId: req.user.id, email: req.user.email },
            process.env.JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRES_IN }
        );
        
        res.redirect(`${process.env.FRONTEND_URL}/auth/success?token=${token}`);
    }
);

// GitHub OAuth routes
router.get('/github',
    passport.authenticate('github', { scope: ['user:email'] })
);

router.get('/github/callback',
    passport.authenticate('github', { session: false }),
    async (req, res) => {
        const token = jwt.sign(
            { userId: req.user.id, email: req.user.email },
            process.env.JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRES_IN }
        );
        
        res.redirect(`${process.env.FRONTEND_URL}/auth/success?token=${token}`);
    }
);

// Microsoft OAuth routes
router.get('/microsoft',
    passport.authenticate('microsoft', { scope: ['user.read'] })
);

router.get('/microsoft/callback',
    passport.authenticate('microsoft', { session: false }),
    async (req, res) => {
        const token = jwt.sign(
            { userId: req.user.id, email: req.user.email },
            process.env.JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRES_IN }
        );
        
        res.redirect(`${process.env.FRONTEND_URL}/auth/success?token=${token}`);
    }
);

// Token refresh endpoint
router.post('/refresh', async (req, res) => {
    try {
        const { refreshToken } = req.body;
        
        if (!refreshToken) {
            return res.status(401).json({ error: 'Refresh token required' });
        }
        
        const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
        const user = await User.findById(decoded.userId);
        
        if (!user) {
            return res.status(401).json({ error: 'User not found' });
        }
        
        const newToken = jwt.sign(
            { userId: user.id, email: user.email },
            process.env.JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRES_IN }
        );
        
        res.json({ token: newToken });
    } catch (error) {
        res.status(401).json({ error: 'Invalid refresh token' });
    }
});

// Logout endpoint
router.post('/logout', (req, res) => {
    // In a production app, you'd want to blacklist the token
    res.json({ message: 'Logged out successfully' });
});

export default router;
```

### 3. JWT Middleware

#### Authentication Middleware
```javascript
// middleware/auth.js
import jwt from 'jsonwebtoken';
import { User } from '../models/User.js';

export const authenticateToken = async (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }
    
    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.userId);
        
        if (!user) {
            return res.status(401).json({ error: 'User not found' });
        }
        
        req.user = user;
        next();
    } catch (error) {
        return res.status(403).json({ error: 'Invalid or expired token' });
    }
};

export const optionalAuth = async (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (token) {
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            const user = await User.findById(decoded.userId);
            req.user = user;
        } catch (error) {
            // Token invalid, but continue without user
            req.user = null;
        }
    }
    
    next();
};
```

### 4. Database Models (Prisma)

#### Prisma Schema
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(uuid())
  email       String   @unique
  name        String
  avatarUrl   String?  @map("avatar_url")
  provider    String
  providerId  String   @map("provider_id")
  preferences Json     @default("{}")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  conversations Conversation[]

  @@unique([provider, providerId])
  @@map("users")
}

model Conversation {
  id              String   @id @default(uuid())
  userId          String   @map("user_id")
  title           String
  openaiThreadId  String?  @unique @map("openai_thread_id")
  agentType       String   @default("general") @map("agent_type")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages Message[]

  @@map("conversations")
}

model Message {
  id             String   @id @default(uuid())
  conversationId String   @map("conversation_id")
  role           String
  content        String
  metadata       Json     @default("{}")
  createdAt      DateTime @default(now()) @map("created_at")

  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@map("messages")
}
```

#### User Model Methods
```javascript
// models/User.js
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export class User {
    static async findOrCreate({ provider, providerId, email, name, avatarUrl }) {
        try {
            // Try to find existing user
            let user = await prisma.user.findUnique({
                where: {
                    provider_providerId: {
                        provider,
                        providerId
                    }
                }
            });

            if (!user) {
                // Create new user
                user = await prisma.user.create({
                    data: {
                        provider,
                        providerId,
                        email,
                        name,
                        avatarUrl
                    }
                });
            } else {
                // Update existing user info
                user = await prisma.user.update({
                    where: { id: user.id },
                    data: {
                        name,
                        avatarUrl,
                        updatedAt: new Date()
                    }
                });
            }

            return user;
        } catch (error) {
            throw new Error(`Failed to find or create user: ${error.message}`);
        }
    }

    static async findById(id) {
        return await prisma.user.findUnique({
            where: { id }
        });
    }

    static async updatePreferences(userId, preferences) {
        return await prisma.user.update({
            where: { id: userId },
            data: { preferences }
        });
    }
}
```

## Frontend Implementation Details

### 1. Authentication Context

#### React Auth Context
```javascript
// contexts/AuthContext.jsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';

const AuthContext = createContext();

const authReducer = (state, action) => {
    switch (action.type) {
        case 'LOGIN_START':
            return { ...state, loading: true, error: null };
        case 'LOGIN_SUCCESS':
            return { 
                ...state, 
                loading: false, 
                isAuthenticated: true, 
                user: action.payload.user,
                token: action.payload.token 
            };
        case 'LOGIN_ERROR':
            return { ...state, loading: false, error: action.payload };
        case 'LOGOUT':
            return { 
                ...state, 
                isAuthenticated: false, 
                user: null, 
                token: null 
            };
        case 'TOKEN_REFRESH':
            return { ...state, token: action.payload };
        default:
            return state;
    }
};

const initialState = {
    isAuthenticated: false,
    user: null,
    token: null,
    loading: false,
    error: null
};

export const AuthProvider = ({ children }) => {
    const [state, dispatch] = useReducer(authReducer, initialState);

    useEffect(() => {
        // Check for stored token on app load
        const token = localStorage.getItem('jarvis_token');
        if (token) {
            // Verify token and get user info
            verifyToken(token);
        }
    }, []);

    const verifyToken = async (token) => {
        try {
            const response = await fetch('/api/auth/verify', {
                headers: { Authorization: `Bearer ${token}` }
            });
            
            if (response.ok) {
                const user = await response.json();
                dispatch({ 
                    type: 'LOGIN_SUCCESS', 
                    payload: { user, token } 
                });
            } else {
                localStorage.removeItem('jarvis_token');
            }
        } catch (error) {
            localStorage.removeItem('jarvis_token');
        }
    };

    const login = (provider) => {
        dispatch({ type: 'LOGIN_START' });
        window.location.href = `/api/auth/${provider}`;
    };

    const logout = () => {
        localStorage.removeItem('jarvis_token');
        dispatch({ type: 'LOGOUT' });
    };

    const handleAuthSuccess = (token) => {
        localStorage.setItem('jarvis_token', token);
        verifyToken(token);
    };

    return (
        <AuthContext.Provider value={{
            ...state,
            login,
            logout,
            handleAuthSuccess
        }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
```

### 2. Authentication Components

#### Login Component
```javascript
// components/Login.jsx
import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const Login = () => {
    const { login, loading } = useAuth();

    const handleLogin = (provider) => {
        login(provider);
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-900">
            <div className="max-w-md w-full space-y-8">
                <div>
                    <h2 className="mt-6 text-center text-3xl font-extrabold text-white">
                        Sign in to JARVIS
                    </h2>
                    <p className="mt-2 text-center text-sm text-gray-400">
                        Choose your preferred authentication method
                    </p>
                </div>
                
                <div className="space-y-4">
                    <button
                        onClick={() => handleLogin('google')}
                        disabled={loading}
                        className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                    >
                        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                            {/* Google icon SVG */}
                        </svg>
                        Continue with Google
                    </button>
                    
                    <button
                        onClick={() => handleLogin('github')}
                        disabled={loading}
                        className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50"
                    >
                        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                            {/* GitHub icon SVG */}
                        </svg>
                        Continue with GitHub
                    </button>
                    
                    <button
                        onClick={() => handleLogin('microsoft')}
                        disabled={loading}
                        className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                            {/* Microsoft icon SVG */}
                        </svg>
                        Continue with Microsoft
                    </button>
                </div>
            </div>
        </div>
    );
};

export default Login;
```

#### Auth Success Handler
```javascript
// components/AuthSuccess.jsx
import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const AuthSuccess = () => {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const { handleAuthSuccess } = useAuth();

    useEffect(() => {
        const token = searchParams.get('token');
        if (token) {
            handleAuthSuccess(token);
            navigate('/dashboard');
        } else {
            navigate('/login');
        }
    }, [searchParams, handleAuthSuccess, navigate]);

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-900">
            <div className="text-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-4 text-white">Completing authentication...</p>
            </div>
        </div>
    );
};

export default AuthSuccess;
```

### 3. Protected Routes

#### Route Protection Component
```javascript
// components/ProtectedRoute.jsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ProtectedRoute = ({ children }) => {
    const { isAuthenticated, loading } = useAuth();

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-900">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return isAuthenticated ? children : <Navigate to="/login" />;
};

export default ProtectedRoute;
```

### 4. API Integration

#### Authenticated API Client
```javascript
// utils/api.js
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

const apiClient = axios.create({
    baseURL: API_BASE_URL,
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('jarvis_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor to handle token expiry
apiClient.interceptors.response.use(
    (response) => response,
    async (error) => {
        if (error.response?.status === 401) {
            localStorage.removeItem('jarvis_token');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

export default apiClient;
```

## Security Considerations

### 1. Token Security
- JWT tokens stored in localStorage (consider httpOnly cookies for production)
- Token expiration and refresh mechanism
- Secure token transmission over HTTPS only

### 2. OAuth Security
- State parameter validation to prevent CSRF attacks
- Secure redirect URI validation
- PKCE implementation for enhanced security

### 3. Database Security
- Parameterized queries to prevent SQL injection
- User input validation and sanitization
- Proper indexing for performance and security

### 4. API Security
- Rate limiting per user and IP
- CORS configuration for allowed origins
- Request validation middleware

This technical specification provides the complete implementation details for OAuth authentication in the JARVIS AI Assistant.