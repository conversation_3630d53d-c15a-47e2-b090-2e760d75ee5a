# JARVIS AI Assistant - Hetzner Deployment Checklist

## Pre-Deployment Assessment ✅

### Current Deployment Readiness
- [x] **Docker Configuration**: Multi-stage Dockerfile with proper build stages
- [x] **Container Orchestration**: docker-compose.yml with health checks
- [x] **Reverse Proxy**: nginx configuration with WebSocket support
- [x] **Environment Management**: Comprehensive .env templates
- [x] **Documentation**: Complete deployment guide in HETZNER_DEPLOYMENT.md
- [x] **Security**: Basic rate limiting and CORS configured

### Identified Gaps for OAuth Integration
- [ ] **Database Service**: PostgreSQL not included in docker-compose
- [ ] **OAuth Environment Variables**: Missing OAuth provider configurations
- [ ] **Session Storage**: No persistent session store configured
- [ ] **SSL Configuration**: OAuth requires HTTPS for production
- [ ] **Database Migrations**: No migration system in place

## Enhanced Hetzner Deployment Plan

### 1. Server Specifications (Updated for Database)
```bash
# Recommended specs for JARVIS with PostgreSQL
- CPU: 4 vCPUs (CX31 or better) - increased for database
- RAM: 8GB minimum - increased for PostgreSQL
- Storage: 80GB SSD - increased for database and logs
- OS: Ubuntu 22.04 LTS
- Network: IPv4 + IPv6
```

### 2. Docker Compose Enhancements

#### Updated docker-compose.yml Structure
```yaml
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # JARVIS Application
  jarvis-app:
    build: .
    ports:
      - "5050:5050"
    environment:
      - NODE_ENV=production
      - PORT=5050
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_ASSISTANT_ID=${OPENAI_ASSISTANT_ID}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      - MICROSOFT_CLIENT_ID=${MICROSOFT_CLIENT_ID}
      - MICROSOFT_CLIENT_SECRET=${MICROSOFT_CLIENT_SECRET}
      - JWT_SECRET=${JWT_SECRET}
      - SESSION_SECRET=${SESSION_SECRET}
      - CORS_ORIGIN=${CORS_ORIGIN}
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5050/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - jarvis-app
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
```

### 3. Environment Variables (Complete Set)

#### Production .env Template
```bash
# Database Configuration
POSTGRES_DB=jarvis_production
POSTGRES_USER=jarvis_user
POSTGRES_PASSWORD=your_secure_database_password
DATABASE_URL=********************************************************************/jarvis_production

# OpenAI Configuration
OPENAI_API_KEY=sk-proj-your-actual-key-here
OPENAI_ASSISTANT_ID=asst_your-assistant-id-here

# OAuth Provider Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=https://your-domain.com/auth/google/callback

GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_CALLBACK_URL=https://your-domain.com/auth/github/callback

MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret
MICROSOFT_CALLBACK_URL=https://your-domain.com/auth/microsoft/callback

# Security Configuration
JWT_SECRET=your_jwt_secret_key_minimum_32_characters
SESSION_SECRET=your_session_secret_key_minimum_32_characters

# Server Configuration
NODE_ENV=production
PORT=5050
CORS_ORIGIN=https://your-domain.com
FRONTEND_URL=https://your-domain.com
BACKEND_URL=https://your-domain.com

# n8n Integration (Optional)
N8N_BASE_URL=https://n8n.scrapha.com
N8N_API_KEY=your-n8n-api-key-here
```

### 4. SSL/HTTPS Configuration for OAuth

#### Let's Encrypt Setup
```bash
# Install certbot
apt install certbot -y

# Get SSL certificate
certbot certonly --standalone -d your-domain.com

# Create SSL directory
mkdir -p /opt/jarvis/ssl

# Copy certificates
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem /opt/jarvis/ssl/cert.pem
cp /etc/letsencrypt/live/your-domain.com/privkey.pem /opt/jarvis/ssl/key.pem

# Set proper permissions
chmod 600 /opt/jarvis/ssl/*.pem
```

#### Enhanced nginx.conf for OAuth
```nginx
events {
    worker_connections 1024;
}

http {
    upstream jarvis-app {
        server jarvis-app:5050;
    }

    # Redirect HTTP to HTTPS
    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    # HTTPS Configuration
    server {
        listen 443 ssl http2;
        server_name your-domain.com;
        
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        
        # SSL Security
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;
        
        # Security Headers
        add_header Strict-Transport-Security "max-age=63072000" always;
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        
        location / {
            proxy_pass http://jarvis-app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
        
        # OAuth callback routes
        location /auth/ {
            proxy_pass http://jarvis-app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

### 5. Database Initialization

#### Database Migration Script
```sql
-- database/init/01-init.sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    avatar_url TEXT,
    provider VARCHAR(50) NOT NULL,
    provider_id VARCHAR(255) NOT NULL,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(provider, provider_id)
);

-- Conversations table
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    openai_thread_id VARCHAR(255) UNIQUE,
    agent_type VARCHAR(50) DEFAULT 'general',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Messages table
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_provider ON users(provider, provider_id);
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_thread_id ON conversations(openai_thread_id);
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
```

### 6. Deployment Steps (Updated)

#### Step 1: Server Preparation
```bash
# Connect to server
ssh root@your-server-ip

# Update system
apt update && apt upgrade -y

# Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
apt install docker-compose-plugin -y

# Create application directory
mkdir -p /opt/jarvis
cd /opt/jarvis
```

#### Step 2: Application Deployment
```bash
# Clone repository
git clone https://github.com/your-username/jarvis-frontend.git .

# Create environment file
cp .env.docker .env
nano .env  # Configure all environment variables

# Create database initialization directory
mkdir -p database/init

# Create SSL directory
mkdir -p ssl

# Set up SSL certificates (if using custom domain)
# ... SSL setup steps ...

# Build and start services
docker compose up -d

# Check service status
docker compose ps
docker compose logs -f jarvis-app
```

#### Step 3: OAuth Provider Configuration

##### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URI: `https://your-domain.com/auth/google/callback`

##### GitHub OAuth Setup
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create new OAuth App
3. Set Authorization callback URL: `https://your-domain.com/auth/github/callback`

##### Microsoft OAuth Setup
1. Go to [Azure Portal](https://portal.azure.com/)
2. Register new application
3. Add redirect URI: `https://your-domain.com/auth/microsoft/callback`

### 7. Monitoring and Maintenance

#### Health Check Commands
```bash
# Check all services
docker compose ps

# Check application health
curl https://your-domain.com/health

# Check database connection
docker compose exec postgres pg_isready -U jarvis_user -d jarvis_production

# View logs
docker compose logs -f jarvis-app
docker compose logs -f postgres
```

#### Backup Strategy
```bash
# Database backup script
#!/bin/bash
BACKUP_DIR="/opt/jarvis/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup database
docker compose exec postgres pg_dump -U jarvis_user jarvis_production > $BACKUP_DIR/db_backup_$DATE.sql

# Backup uploads
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz uploads/

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### 8. Security Hardening

#### Firewall Configuration
```bash
# Configure UFW
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw deny 5432/tcp  # Block direct database access
ufw enable
```

#### Docker Security
```bash
# Run containers as non-root user
# Add to Dockerfile:
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs
```

### 9. Cost Estimation (Updated)

#### Hetzner Cloud Costs
- **CX31 Server**: ~€8.90/month (4 vCPUs, 8GB RAM, 80GB SSD)
- **Domain**: ~€10-15/year
- **Backup Storage**: ~€2-5/month
- **Total**: ~€10-15/month

### 10. Troubleshooting Guide

#### Common Issues
1. **Database Connection Failed**
   - Check PostgreSQL service status
   - Verify DATABASE_URL format
   - Check network connectivity between containers

2. **OAuth Redirect Mismatch**
   - Verify callback URLs in provider settings
   - Check HTTPS configuration
   - Validate CORS_ORIGIN setting

3. **SSL Certificate Issues**
   - Renew Let's Encrypt certificates
   - Check certificate file permissions
   - Verify nginx SSL configuration

This enhanced deployment checklist ensures your JARVIS AI Assistant is ready for production deployment on Hetzner with full OAuth authentication support.