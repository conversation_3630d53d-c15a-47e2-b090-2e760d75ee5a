#!/bin/bash

# Production Testing Script for JARVIS Application
echo "🧪 Starting Production Testing..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

# Test functions
test_endpoint() {
    local method=$1
    local url=$2
    local description=$3
    local expected_codes=${4:-"200 201 302"}
    
    echo -n "Testing $description... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    else
        response=$(curl -s -o /dev/null -w "%{http_code}" -X "$method" "$url" 2>/dev/null || echo "000")
    fi
    
    # Check if response code is in expected codes
    if echo "$expected_codes" | grep -q "$response"; then
        echo -e "${GREEN}✅ PASS${NC} (HTTP $response)"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC} (HTTP $response)"
        ((TESTS_FAILED++))
    fi
}

test_database() {
    echo -n "Testing database connectivity... "
    
    if [ -f "backend/database.sqlite" ]; then
        # Check if users table exists
        users_count=$(sqlite3 backend/database.sqlite "SELECT COUNT(*) FROM users;" 2>/dev/null || echo "0")
        if [ "$users_count" -ge 0 ]; then
            echo -e "${GREEN}✅ PASS${NC} (users table exists)"
            ((TESTS_PASSED++))
        else
            echo -e "${RED}❌ FAIL${NC} (users table missing)"
            ((TESTS_FAILED++))
        fi
    else
        echo -e "${RED}❌ FAIL${NC} (database file missing)"
        ((TESTS_FAILED++))
    fi
}

test_mock_auth() {
    echo -n "Testing mock authentication... "
    
    response=$(curl -s -X POST http://localhost:5050/auth/mock/login \
        -H "Content-Type: application/json" \
        -d '{"provider": "google"}' 2>/dev/null || echo "{}")
    
    if echo "$response" | grep -q "token"; then
        echo -e "${GREEN}✅ PASS${NC} (JWT token generated)"
        ((TESTS_PASSED++))
        
        # Extract token for further testing
        token=$(echo "$response" | sed 's/.*"token":"\([^"]*\)".*/\1/')
        
        # Test user endpoint with token
        echo -n "Testing user endpoint with token... "
        user_response=$(curl -s -o /dev/null -w "%{http_code}" \
            http://localhost:5050/auth/user \
            -H "Authorization: Bearer $token" 2>/dev/null || echo "000")
        
        if [ "$user_response" = "200" ]; then
            echo -e "${GREEN}✅ PASS${NC} (user endpoint accessible)"
            ((TESTS_PASSED++))
        else
            echo -e "${RED}❌ FAIL${NC} (user endpoint failed: $user_response)"
            ((TESTS_FAILED++))
        fi
    else
        echo -e "${RED}❌ FAIL${NC} (no token received)"
        ((TESTS_FAILED++))
    fi
}

check_service_health() {
    local url=$1
    local description=$2
    local max_attempts=10
    local attempt=1
    
    echo -n "Checking $description... "
    
    while [ $attempt -le $max_attempts ]; do
        response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
        if [ "$response" = "200" ]; then
            echo -e "${GREEN}✅ READY${NC}"
            return 0
        fi
        
        echo -n "."
        sleep 1
        ((attempt++))
    done
    
    echo -e "${RED}❌ TIMEOUT${NC}"
    return 1
}

# Main testing function
main() {
    echo "${BLUE}================================${NC}"
    echo "${BLUE}  JARVIS Production Testing    ${NC}"
    echo "${BLUE}================================${NC}"
    echo
    
    # Check if services are running
    echo "${YELLOW}🔍 Checking service health...${NC}"
    check_service_health "http://localhost:5050/api/health" "Backend API"
    check_service_health "http://localhost:5173" "Frontend"
    echo
    
    # Run tests
    echo "${YELLOW}🧪 Running automated tests...${NC}"
    
    # Backend tests
    echo "${BLUE}Backend Tests:${NC}"
    test_endpoint "GET" "http://localhost:5050/api/health" "Health Check"
    test_endpoint "GET" "http://localhost:5050/auth/me" "Auth User Endpoint" "401"
    test_endpoint "POST" "http://localhost:5050/auth/mock/login" "Mock Login Endpoint"
    
    # Database tests
    echo
    echo "${BLUE}Database Tests:${NC}"
    test_database
    
    # Authentication flow
    echo
    echo "${BLUE}Authentication Flow:${NC}"
    test_mock_auth
    
    # Frontend tests
    echo
    echo "${BLUE}Frontend Tests:${NC}"
    test_endpoint "GET" "http://localhost:5173" "Frontend Load"
    
    # Environment checks
    echo
    echo "${BLUE}Environment Checks:${NC}"
    if [ -f "backend/.env.example" ]; then
        echo -e "${GREEN}✅ Environment template exists${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ Environment template missing${NC}"
        ((TESTS_FAILED++))
    fi
    
    if [ -f "backend/database.sqlite" ]; then
        echo -e "${GREEN}✅ Database file exists${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ Database file missing${NC}"
        ((TESTS_FAILED++))
    fi
    
    # Summary
    echo
    echo "${BLUE}================================${NC}"
    echo "${BLUE}  Testing Summary              ${NC}"
    echo "${BLUE}================================${NC}"
    echo
    echo "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
    echo "Tests Failed: ${RED}$TESTS_FAILED${NC}"
    echo
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "${GREEN}🎉 All tests passed! Ready for production.${NC}"
        exit 0
    else
        echo -e "${RED}❌ Some tests failed. Please fix before production.${NC}"
        exit 1
    fi
}

# Run main function
main
