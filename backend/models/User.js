import { run, get, all } from '../config/database.js';
import crypto from 'crypto';

export class User {
  static async findOrCreate({ provider, providerId, email, name, avatarUrl }) {
    try {
      // Check if user exists
      const existingUser = await get(
        'SELECT * FROM users WHERE provider = ? AND provider_id = ?',
        [provider, providerId]
      );
      
      if (existingUser) {
        // Update last login
        await run(
          'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [existingUser.id]
        );
        return existingUser;
      }
      
      // Create new user
      const id = crypto.randomUUID();
      await run(
        `INSERT INTO users (id, email, name, avatar_url, provider, provider_id) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [id, email, name, avatarUrl, provider, providerId]
      );
      
      return await this.findById(id);
      
    } catch (error) {
      console.error('Error in findOrCreate:', error);
      throw error;
    }
  }

  static async findById(id) {
    try {
      const user = await get('SELECT * FROM users WHERE id = ?', [id]);
      return user || null;
    } catch (error) {
      console.error('Error finding user by ID:', error);
      return null;
    }
  }

  static async updatePreferences(id, preferences) {
    try {
      await run(
        'UPDATE users SET preferences = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [JSON.stringify(preferences), id]
      );
      return await this.findById(id);
    } catch (error) {
      console.error('Error updating preferences:', error);
      throw error;
    }
  }
}
