⚠️  Google OAuth not configured - missing GOO<PERSON>LE_CLIENT_ID or GOO<PERSON>LE_CLIENT_SECRET
⚠️  GitHub OAuth not configured - missing GITHUB_CLIENT_ID or GITHUB_CLIENT_SECRET
⚠️  Microsoft OAuth not configured - missing MICROSOFT_CLIENT_ID or MICROSOFT_CLIENT_SECRET
--- Checking Environment Variables ---
NODE_ENV: development
CORS_ORIGIN: Not Set
OPENAI_API_KEY: MISSING!
OPENAI_ASSISTANT_ID: MISSING!
N8N_BASE_URL: Not Set
N8N_API_KEY: Not Set
GOOGLE_CLIENT_ID: MISSING!
GITHUB_CLIENT_ID: MISSING!
MICROSOFT_CLIENT_ID: MISSING!
SESSION_SECRET: MISSING!
JWT_SECRET: MISSING!
------------------------------------
FATAL: Missing required OpenAI environment variables. Server cannot start.
✅ Database initialized successfully
⚠️  OpenAI API key not found - AI features will be disabled
⚠️  OpenAI Assistant ID not found - AI features will be disabled
🚀 JARVIS Unified Backend running on port 5050
