import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import session from 'express-session';
import passport from 'passport';
import jwt from 'jsonwebtoken';
import OpenAI from 'openai';
import multer from 'multer';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import { realtime } from '@openai/agents';
const { RealtimeAgent } = realtime;

// Import passport configuration
import './config/passport.js';
import { initDatabase } from './config/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables for local dev, but rely on Railway's env in production
// dotenv.config({ path: join(__dirname, '.env') });

// Configure multer for file uploads
const uploadDir = join(__dirname, 'uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir);
}
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});
const upload = multer({ storage });

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 5050;

// Initialize OpenAI client (optional for testing)
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
}) : null;

// Assistant ID (optional for testing)
const ASSISTANT_ID = process.env.OPENAI_ASSISTANT_ID;

// --- Middleware ---
app.use(helmet());

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100,
  message: 'Too many requests from this IP, please try again later.',
});
app.use('/api/', limiter);

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Initialize Passport
app.use(passport.initialize());
app.use(passport.session());

// CORS configuration
const corsOptions = {
  origin: process.env.NODE_ENV === 'production' 
    ? [
        'https://jarvis-frontend.windsurf.build',
        'https://6larryaj.netlify.app'
      ]
    : ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Import and use authentication routes
import authRoutes from './routes/auth.js';
import mockAuthRoutes from './routes/mock-auth.js';

// Use mock routes for testing (comment out for production)
app.use('/auth', mockAuthRoutes);
app.use('/auth', authRoutes);

// --- Thread Management ---
const threadStore = new Map();

// --- Unified Tool Definitions ---
const toolFunctions = {
  get_weather: async (args) => {
    const { location } = args;
    return { location, temperature: "72°F", condition: "Sunny", humidity: "45%", timestamp: new Date().toISOString() };
  },
  send_email: async (args) => {
    const { to, subject, body } = args;
    console.log(`Sending email to ${to}: ${subject}`);
    return { success: true, message: `Email sent to ${to}`, timestamp: new Date().toISOString() };
  },
  trigger_n8n_workflow: async (args) => {
    const { workflow_name, data } = args;
    try {
      const n8nBaseUrl = process.env.N8N_BASE_URL || 'https://n8n.scrapha.com';
      const webhookUrl = `${n8nBaseUrl}/webhook/${workflow_name}`;
      console.log(`Triggering n8n workflow: ${workflow_name}`, data);
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': process.env.N8N_API_KEY ? `Bearer ${process.env.N8N_API_KEY}` : undefined },
        body: JSON.stringify({ ...data, timestamp: new Date().toISOString(), source: 'jarvis-ai-unified' })
      });
      if (!response.ok) throw new Error(`N8N workflow failed: ${response.statusText}`);
      const result = await response.json();
      return { success: true, workflow: workflow_name, result, triggered_at: new Date().toISOString() };
    } catch (error) {
      console.error(`Error triggering n8n workflow ${workflow_name}:`, error);
      return { success: false, workflow: workflow_name, error: error.message, triggered_at: new Date().toISOString() };
    }
  },
  search_web: async (args) => {
    const { query } = args;
    return { query, results: [{ title: "Sample Search Result", url: "https://example.com", snippet: "This is a mock search result." }], timestamp: new Date().toISOString() };
  }
};

const assistantTools = [
  { type: "function", function: { name: "get_weather", description: "Get the current weather for a location", parameters: { type: "object", properties: { location: { type: "string" } }, required: ["location"] } } },
  { type: "function", function: { name: "send_email", description: "Send an email", parameters: { type: "object", properties: { to: { type: "string" }, subject: { type: "string" }, body: { type: "string" } }, required: ["to", "subject", "body"] } } },
  { type: "function", function: { name: "trigger_n8n_workflow", description: "Trigger an n8n workflow", parameters: { type: "object", properties: { workflow_name: { type: "string" }, data: { type: "object" } }, required: ["workflow_name"] } } },
  { type: "function", function: { name: "search_web", description: "Search the web for information", parameters: { type: "object", properties: { query: { type: "string" } }, required: ["query"] } } }
];

// -- Realtime Agent Definitions --
const agents = []; // Main registry for all agents

const generalAgent = new RealtimeAgent({
  name: 'general',
  description: 'A general-purpose AI assistant for a wide range of tasks.',
  instructions: 'You are a helpful general-purpose AI assistant. You can answer questions, provide information, and assist with various tasks. You can hand off to other specialized agents when needed.',
  tools: assistantTools,
  handoffs: [] // Will be populated later
});

const emailAgent = new RealtimeAgent({
  name: 'email',
  description: 'An agent specialized in composing and sending emails.',
  instructions: 'You are a specialized email assistant. You can draft, format, and send emails. You can hand off to other agents for tasks outside of email composition.',
  tools: [assistantTools.find(t => t.function.name === 'send_email')],
  handoffs: []
});

const financialAgent = new RealtimeAgent({
  name: 'finance',
  description: 'An agent for financial queries and tasks.',
  instructions: 'You are a specialized financial assistant. You can provide financial advice, track expenses, and help with budgeting. You can also send emails and trigger n8n workflows for financial tasks.',
  tools: [assistantTools.find(t => t.function.name === 'send_email'), assistantTools.find(t => t.function.name === 'trigger_n8n_workflow')],
  handoffs: []
});

const taskAgent = new RealtimeAgent({
  name: 'tasks',
  description: 'An agent for managing tasks and using n8n workflows.',
  instructions: 'You are a task management assistant. You can create, update, and manage tasks. You are an expert in using n8n workflows to automate complex processes. You can hand off to other agents for non-task-related queries.',
  tools: assistantTools,
  handoffs: []
});

// Set up agent handoffs
financialAgent.handoffs = [taskAgent, emailAgent, generalAgent];
taskAgent.handoffs = [financialAgent, emailAgent, generalAgent];
emailAgent.handoffs = [financialAgent, taskAgent, generalAgent];
generalAgent.handoffs = [financialAgent, taskAgent, emailAgent];

agents.push(generalAgent, financialAgent, taskAgent, emailAgent);

// --- ROUTES ---

// Only serve static files in production mode
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(join(__dirname, 'public')));
}

// -- Assistant API Routes --
app.post('/api/thread', (req, res) => {
  if (!openai) {
    return res.status(400).json({ error: 'OpenAI API key not configured' });
  }
  openai.beta.threads.create()
    .then(thread => {
      res.json({ threadId: thread.id });
    })
    .catch(error => {
      console.error('Error creating thread:', error);
      res.status(500).json({ error: 'Failed to create thread', details: error.message });
    });
});
app.post('/api/chat', upload.array('files'), async (req, res) => {
  try {
    const { message, threadId } = req.body;
    const files = req.files;

    if (!threadId) {
      return res.status(400).json({ error: 'threadId is required' });
    }
    
    let content = message || '';
    if (files && files.length > 0) {
        const fileIds = [];
        for (const file of files) {
            const openaiFile = await openai.files.create({
                file: fs.createReadStream(file.path),
                purpose: 'assistants',
            });
            fileIds.push(openaiFile.id);
        }
        content = { type: 'text', text: message, file_ids: fileIds };
    }

    await openai.beta.threads.messages.create(threadId, {
      role: 'user',
      content: content,
    });

    const run = await openai.beta.threads.runs.create(threadId, {
      assistant_id: ASSISTANT_ID,
    });

    let runStatus = await openai.beta.threads.runs.retrieve(threadId, run.id);
    while (runStatus.status === 'queued' || runStatus.status === 'in_progress') {
      await new Promise(resolve => setTimeout(resolve, 1000));
      runStatus = await openai.beta.threads.runs.retrieve(threadId, run.id);
    }

    // Handle tool calls
    if (runStatus.status === 'requires_action') {
      const toolCalls = runStatus.required_action.submit_tool_outputs.tool_calls;
      const toolOutputs = [];

      for (const toolCall of toolCalls) {
        const functionName = toolCall.function.name;
        const args = JSON.parse(toolCall.function.arguments);

        if (toolFunctions[functionName]) {
          const result = await toolFunctions[functionName](args);
          toolOutputs.push({
            tool_call_id: toolCall.id,
            output: JSON.stringify(result),
          });
        }
      }

      await openai.beta.threads.runs.submitToolOutputs(threadId, run.id, {
        tool_outputs: toolOutputs,
      });

      // Wait for completion again
      runStatus = await openai.beta.threads.runs.retrieve(threadId, run.id);
      while (runStatus.status === 'queued' || runStatus.status === 'in_progress') {
        await new Promise(resolve => setTimeout(resolve, 1000));
        runStatus = await openai.beta.threads.runs.retrieve(threadId, run.id);
      }
    }
    
    if (runStatus.status === 'completed') {
        const messages = await openai.beta.threads.messages.list(threadId);
        const lastMessage = messages.data.find(m => m.run_id === run.id && m.role === 'assistant');
        
        if (lastMessage) {
            const responseText = lastMessage.content[0].text.value;
            res.json({
              response: responseText,
              timestamp: new Date().toISOString(),
            });
        } else {
            res.status(500).json({ error: 'No assistant response found' });
        }
    } else {
        res.status(500).json({ error: 'Run failed', details: runStatus.last_error });
    }

  } catch (error) {
    console.error('Error in chat:', error);
    res.status(500).json({ error: 'Failed to process chat message' });
  }
});

app.get('/api/assistant', async (req, res) => {
  try {
    if (!ASSISTANT_ID) {
      return res.status(400).json({ error: 'Assistant ID not configured' });
    }
    const assistant = await openai.beta.assistants.retrieve(ASSISTANT_ID);
    res.json({ id: assistant.id, name: assistant.name, model: assistant.model, tools: assistant.tools });
  } catch (error) {
    console.error('Error getting assistant:', error);
    res.status(500).json({ error: 'Failed to get assistant info' });
  }
});

// -- Realtime API Routes --
app.post('/api/realtime/session', async (req, res) => {
  try {
    const { agent = 'general' } = req.body;
    const selectedAgent = agents.find(a => a.name === agent) || generalAgent;
    
    const response = await fetch('https://api.openai.com/v1/realtime/sessions', {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'gpt-4o-realtime-preview-2024-12-17',
        voice: 'verse',
        instructions: selectedAgent.instructions,
        tools: selectedAgent.tools,
        tool_choice: 'auto'
      })
    });

    if (!response.ok) {
        const errorBody = await response.text();
        console.error('OpenAI API error:', response.status, errorBody);
        throw new Error(`OpenAI API error: ${response.status}`);
    }

    const sessionData = await response.json();
    res.json({ ...sessionData, agent: selectedAgent.name, availableAgents: agents.map(a => ({ name: a.name, description: a.handoffDescription })) });
  } catch (error) {
    console.error('Error creating realtime session:', error);
    res.status(500).json({ error: 'Failed to create realtime session' });
  }
});

// --- Server Lifecycle ---

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Handle static file serving based on environment
if (process.env.NODE_ENV === 'production') {
  // Production mode - serve built frontend files
  const publicPath = join(__dirname, 'public');
  if (fs.existsSync(publicPath)) {
    app.use(express.static(publicPath));

    // Serve index.html for all other routes (SPA support)
    app.get('*', (req, res) => {
      const indexPath = join(publicPath, 'index.html');
      if (fs.existsSync(indexPath)) {
        res.sendFile(indexPath);
      } else {
        res.status(404).json({ error: 'Frontend not built - run npm run build in frontend directory' });
      }
    });
  } else {
    console.error('❌ Public directory not found in production mode');
    app.use((req, res) => {
      res.status(500).json({ error: 'Frontend files not found' });
    });
  }
} else {
  // Development mode - frontend served separately by Vite
  console.log('⚠️  Development mode - frontend served separately on port 5173');

  // 404 handler for non-API routes in development
  app.use((req, res) => {
    if (req.path.startsWith('/api/')) {
      res.status(404).json({ error: 'API route not found' });
    } else {
      res.status(404).json({
        error: 'Route not found',
        message: 'In development mode, frontend is served on http://localhost:5173'
      });
    }
  });
}

// Start server
app.listen(PORT, async () => {
  console.log('--- Checking Environment Variables ---');
  console.log(`NODE_ENV: ${process.env.NODE_ENV || 'development'}`);
  console.log(`CORS_ORIGIN: ${process.env.CORS_ORIGIN ? 'Set' : 'Not Set'}`);
  console.log(`OPENAI_API_KEY: ${process.env.OPENAI_API_KEY ? 'Set' : 'MISSING!'}`);
  console.log(`OPENAI_ASSISTANT_ID: ${process.env.OPENAI_ASSISTANT_ID ? 'Set' : 'MISSING!'}`);
  console.log(`N8N_BASE_URL: ${process.env.N8N_BASE_URL ? 'Set' : 'Not Set'}`);
  console.log(`N8N_API_KEY: ${process.env.N8N_API_KEY ? 'Set' : 'Not Set'}`);
  
  // OAuth environment variables
  console.log(`GOOGLE_CLIENT_ID: ${process.env.GOOGLE_CLIENT_ID ? 'Set' : 'MISSING!'}`);
  console.log(`GITHUB_CLIENT_ID: ${process.env.GITHUB_CLIENT_ID ? 'Set' : 'MISSING!'}`);
  console.log(`MICROSOFT_CLIENT_ID: ${process.env.MICROSOFT_CLIENT_ID ? 'Set' : 'MISSING!'}`);
  console.log(`SESSION_SECRET: ${process.env.SESSION_SECRET ? 'Set' : 'MISSING!'}`);
  console.log(`JWT_SECRET: ${process.env.JWT_SECRET ? 'Set' : 'MISSING!'}`);
  console.log('------------------------------------');

  if (!process.env.OPENAI_API_KEY || !process.env.OPENAI_ASSISTANT_ID) {
    console.error('FATAL: Missing required OpenAI environment variables. Server cannot start.');
    // process.exit(1); // We won't exit, but the OpenAI client will likely fail and crash.
  }

  // Initialize database
  try {
    await initDatabase();
    console.log('✅ Database initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize database:', error);
  }

  // Skip OpenAI initialization if no API key
  if (!process.env.OPENAI_API_KEY) {
    console.warn('⚠️  OpenAI API key not found - AI features will be disabled');
  }
  if (!process.env.OPENAI_ASSISTANT_ID) {
    console.warn('⚠️  OpenAI Assistant ID not found - AI features will be disabled');
  }

  console.log(`🚀 JARVIS Unified Backend running on port ${PORT}`);
});
