import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import request from 'supertest'
import express from 'express'
import cors from 'cors'
import helmet from 'helmet'

// Mock OpenAI before importing server modules
vi.mock('openai')

describe('Server API Endpoints', () => {
  let app

  beforeEach(async () => {
    // Create a test Express app with the same middleware
    app = express()
    app.use(helmet())
    app.use(cors())
    app.use(express.json())

    // Add test routes that mirror the actual server
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'test'
      })
    })

    app.post('/api/assistant/message', async (req, res) => {
      const { message, threadId } = req.body

      if (!message) {
        return res.status(400).json({ 
          success: false, 
          error: 'Message is required' 
        })
      }

      // Mock successful response
      res.json({
        success: true,
        response: `Mock response to: ${message}`,
        threadId: threadId || 'new-thread-id'
      })
    })

    app.post('/api/assistant/thread', (req, res) => {
      res.json({
        success: true,
        threadId: 'mock-thread-id'
      })
    })

    app.post('/api/realtime/session', (req, res) => {
      res.json({
        success: true,
        sessionToken: 'mock-session-token'
      })
    })

    app.post('/api/upload', (req, res) => {
      res.json({
        success: true,
        fileId: 'mock-file-id',
        message: 'File uploaded successfully'
      })
    })

    app.post('/api/agent/route', (req, res) => {
      const { query } = req.body
      
      if (!query) {
        return res.status(400).json({
          success: false,
          error: 'Query is required'
        })
      }

      res.json({
        success: true,
        agent: 'general',
        confidence: 0.8,
        query
      })
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Health Check Endpoint', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200)

      expect(response.body).toEqual({
        status: 'ok',
        timestamp: expect.any(String),
        environment: 'test'
      })
    })
  })

  describe('Assistant Message Endpoint', () => {
    it('should handle valid message requests', async () => {
      const testMessage = 'Hello, JARVIS!'
      
      const response = await request(app)
        .post('/api/assistant/message')
        .send({ message: testMessage })
        .expect(200)

      expect(response.body).toEqual({
        success: true,
        response: `Mock response to: ${testMessage}`,
        threadId: expect.any(String)
      })
    })

    it('should reject requests without message', async () => {
      const response = await request(app)
        .post('/api/assistant/message')
        .send({})
        .expect(400)

      expect(response.body).toEqual({
        success: false,
        error: 'Message is required'
      })
    })

    it('should handle message with existing thread ID', async () => {
      const testMessage = 'Follow-up message'
      const existingThreadId = 'existing-thread-123'
      
      const response = await request(app)
        .post('/api/assistant/message')
        .send({ 
          message: testMessage,
          threadId: existingThreadId
        })
        .expect(200)

      expect(response.body.threadId).toBe(existingThreadId)
    })
  })

  describe('Thread Creation Endpoint', () => {
    it('should create a new thread', async () => {
      const response = await request(app)
        .post('/api/assistant/thread')
        .expect(200)

      expect(response.body).toEqual({
        success: true,
        threadId: expect.any(String)
      })
    })
  })

  describe('Realtime Session Endpoint', () => {
    it('should create a realtime session', async () => {
      const response = await request(app)
        .post('/api/realtime/session')
        .expect(200)

      expect(response.body).toEqual({
        success: true,
        sessionToken: expect.any(String)
      })
    })
  })

  describe('File Upload Endpoint', () => {
    it('should handle file uploads', async () => {
      const response = await request(app)
        .post('/api/upload')
        .expect(200)

      expect(response.body).toEqual({
        success: true,
        fileId: expect.any(String),
        message: 'File uploaded successfully'
      })
    })
  })

  describe('Agent Routing Endpoint', () => {
    it('should route queries to appropriate agent', async () => {
      const testQuery = 'What is the weather like?'
      
      const response = await request(app)
        .post('/api/agent/route')
        .send({ query: testQuery })
        .expect(200)

      expect(response.body).toEqual({
        success: true,
        agent: 'general',
        confidence: expect.any(Number),
        query: testQuery
      })
    })

    it('should reject requests without query', async () => {
      const response = await request(app)
        .post('/api/agent/route')
        .send({})
        .expect(400)

      expect(response.body).toEqual({
        success: false,
        error: 'Query is required'
      })
    })
  })

  describe('CORS and Security Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200)

      // Check for helmet security headers
      expect(response.headers).toHaveProperty('x-content-type-options')
      expect(response.headers).toHaveProperty('x-frame-options')
    })

    it('should handle CORS preflight requests', async () => {
      const response = await request(app)
        .options('/api/assistant/message')
        .set('Origin', 'http://localhost:5173')
        .set('Access-Control-Request-Method', 'POST')
        .expect(204)

      expect(response.headers).toHaveProperty('access-control-allow-origin')
    })
  })
})
