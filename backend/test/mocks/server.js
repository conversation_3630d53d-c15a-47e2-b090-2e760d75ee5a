import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'

// Mock external API handlers
export const handlers = [
  // Mock OpenAI API endpoints
  http.post('https://api.openai.com/v1/assistants/:assistantId', () => {
    return HttpResponse.json({
      id: 'asst_test123',
      object: 'assistant',
      created_at: Date.now(),
      name: 'Test Assistant',
      description: 'Test assistant for unit tests',
      model: 'gpt-4',
      instructions: 'You are a helpful test assistant.',
      tools: []
    })
  }),

  http.post('https://api.openai.com/v1/threads', () => {
    return HttpResponse.json({
      id: 'thread_test123',
      object: 'thread',
      created_at: Date.now(),
      metadata: {}
    })
  }),

  http.post('https://api.openai.com/v1/threads/:threadId/messages', () => {
    return HttpResponse.json({
      id: 'msg_test123',
      object: 'thread.message',
      created_at: Date.now(),
      thread_id: 'thread_test123',
      role: 'assistant',
      content: [
        {
          type: 'text',
          text: {
            value: 'This is a test response from the mock OpenAI API.',
            annotations: []
          }
        }
      ]
    })
  }),

  http.post('https://api.openai.com/v1/threads/:threadId/runs', () => {
    return HttpResponse.json({
      id: 'run_test123',
      object: 'thread.run',
      created_at: Date.now(),
      thread_id: 'thread_test123',
      assistant_id: 'asst_test123',
      status: 'completed',
      model: 'gpt-4'
    })
  }),

  http.get('https://api.openai.com/v1/threads/:threadId/runs/:runId', () => {
    return HttpResponse.json({
      id: 'run_test123',
      object: 'thread.run',
      created_at: Date.now(),
      thread_id: 'thread_test123',
      assistant_id: 'asst_test123',
      status: 'completed',
      model: 'gpt-4'
    })
  })
]

// Setup server with handlers
export const server = setupServer(...handlers)
