import { beforeEach, afterEach } from 'vitest'

// Mock environment variables for testing
process.env.NODE_ENV = 'test'
process.env.OPENAI_API_KEY = 'test-api-key'
process.env.OPENAI_ASSISTANT_ID = 'test-assistant-id'
process.env.PORT = '5051'

// Mock OpenAI API
vi.mock('openai', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      beta: {
        assistants: {
          retrieve: vi.fn().mockResolvedValue({
            id: 'test-assistant-id',
            name: 'Test Assistant',
            instructions: 'Test instructions'
          })
        },
        threads: {
          create: vi.fn().mockResolvedValue({
            id: 'test-thread-id'
          }),
          messages: {
            create: vi.fn().mockResolvedValue({
              id: 'test-message-id',
              content: [{ type: 'text', text: { value: 'Test response' } }]
            }),
            list: vi.fn().mockResolvedValue({
              data: []
            })
          },
          runs: {
            create: vi.fn().mockResolvedValue({
              id: 'test-run-id',
              status: 'completed'
            }),
            retrieve: vi.fn().mockResolvedValue({
              id: 'test-run-id',
              status: 'completed'
            })
          }
        }
      },
      realtime: {
        connect: vi.fn().mockResolvedValue({
          send: vi.fn(),
          close: vi.fn(),
          on: vi.fn()
        })
      }
    }))
  }
})

// Mock WebSocket
global.WebSocket = class MockWebSocket {
  constructor(url) {
    this.url = url
    this.readyState = 1
    this.onopen = null
    this.onclose = null
    this.onmessage = null
    this.onerror = null
    
    setTimeout(() => {
      if (this.onopen) this.onopen()
    }, 0)
  }
  
  send(data) {
    // Mock send functionality
  }
  
  close() {
    this.readyState = 3
    if (this.onclose) this.onclose()
  }
}

// Clean up after each test case
afterEach(() => {
  vi.clearAllMocks()
})

// Reset all mocks before each test
beforeEach(() => {
  vi.clearAllMocks()
})
