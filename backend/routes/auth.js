import express from 'express';
import passport from 'passport';
import jwt from 'jsonwebtoken';

const router = express.Router();

// JWT token generation
const generateToken = (user) => {
  return jwt.sign(
    { id: user.id, email: user.email, name: user.name },
    process.env.JWT_SECRET || 'your-jwt-secret',
    { expiresIn: '24h' }
  );
};

// Authentication middleware
export const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'your-jwt-secret', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

// OAuth routes
router.get('/google', passport.authenticate('google', { scope: ['profile', 'email'] }));

router.get('/google/callback', 
  passport.authenticate('google', { session: false }),
  (req, res) => {
    const token = generateToken(req.user);
    const redirectUrl = process.env.NODE_ENV === 'production' 
      ? 'https://jarvis-frontend.windsurf.build/'
      : 'http://localhost:5173/';
    
    res.redirect(`${redirectUrl}?token=${token}&user=${encodeURIComponent(JSON.stringify(req.user))}`);
  }
);

router.get('/github', passport.authenticate('github', { scope: ['user:email'] }));

router.get('/github/callback',
  passport.authenticate('github', { session: false }),
  (req, res) => {
    const token = generateToken(req.user);
    const redirectUrl = process.env.NODE_ENV === 'production' 
      ? 'https://jarvis-frontend.windsurf.build/'
      : 'http://localhost:5173/';
    
    res.redirect(`${redirectUrl}?token=${token}&user=${encodeURIComponent(JSON.stringify(req.user))}`);
  }
);

router.get('/microsoft', passport.authenticate('microsoft', { scope: ['user.read'] }));

router.get('/microsoft/callback',
  passport.authenticate('microsoft', { session: false }),
  (req, res) => {
    const token = generateToken(req.user);
    const redirectUrl = process.env.NODE_ENV === 'production' 
      ? 'https://jarvis-frontend.windsurf.build/'
      : 'http://localhost:5173/';
    
    res.redirect(`${redirectUrl}?token=${token}&user=${encodeURIComponent(JSON.stringify(req.user))}`);
  }
);

// User info endpoint
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const { User } = await import('../models/User.js');
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json({
      id: user.id,
      email: user.email,
      name: user.name,
      avatarUrl: user.avatar_url,
      preferences: user.preferences
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ error: 'Failed to fetch user info' });
  }
});

// Logout endpoint
router.post('/logout', (req, res) => {
  res.json({ message: 'Logged out successfully' });
});

export default router;
