import express from 'express';
import jwt from 'jsonwebtoken';
import { User } from '../models/User.js';

const router = express.Router();

// Mock user data for testing
const mockUsers = {
  google: {
    id: 'google-mock-user-123',
    email: '<EMAIL>',
    name: 'Test User',
    avatarUrl: 'https://via.placeholder.com/100',
    provider: 'google',
    providerId: 'google-mock-123'
  },
  github: {
    id: 'github-mock-user-456',
    email: '<EMAIL>',
    name: 'Test User',
    avatarUrl: 'https://via.placeholder.com/100',
    provider: 'github',
    providerId: 'github-mock-456'
  },
  microsoft: {
    id: 'microsoft-mock-user-789',
    email: '<EMAIL>',
    name: 'Test User',
    avatarUrl: 'https://via.placeholder.com/100',
    provider: 'microsoft',
    providerId: 'microsoft-mock-789'
  }
};

// Mock login endpoints
router.get('/mock/:provider', (req, res) => {
  const { provider } = req.params;
  
  if (!mockUsers[provider]) {
    return res.status(400).json({ error: 'Invalid provider' });
  }
  
  // Redirect to mock callback
  res.redirect(`/auth/mock/${provider}/callback`);
});

// Mock callback endpoints
router.get('/mock/:provider/callback', async (req, res) => {
  const { provider } = req.params;
  const mockUser = mockUsers[provider];
  
  try {
    // Create or get mock user
    const user = await User.findOrCreate({
      provider: mockUser.provider,
      providerId: mockUser.providerId,
      email: mockUser.email,
      name: mockUser.name,
      avatarUrl: mockUser.avatarUrl
    });
    
    // Generate JWT token - FIXED: Use same payload structure as regular auth
    const token = jwt.sign(
      { id: user.id, email: user.email, name: user.name },
      process.env.JWT_SECRET || 'your-jwt-secret',
      { expiresIn: '24h' }
    );
    
    // Redirect to frontend with token
    const frontendUrl = process.env.NODE_ENV === 'production' 
      ? 'https://jarvis-frontend.windsurf.build' 
      : 'http://localhost:5173';
    
    res.redirect(`${frontendUrl}/?token=${token}&user=${encodeURIComponent(JSON.stringify(user))}`);
    
  } catch (error) {
    console.error('Mock auth error:', error);
    res.status(500).json({ error: 'Mock authentication failed' });
  }
});

// Test endpoint to create a mock user directly
router.post('/mock/login', async (req, res) => {
  try {
    const { provider = 'google' } = req.body;
    const mockUser = mockUsers[provider] || mockUsers.google;
    
    const user = await User.findOrCreate({
      provider: mockUser.provider,
      providerId: mockUser.providerId,
      email: mockUser.email,
      name: mockUser.name,
      avatarUrl: mockUser.avatarUrl
    });
    
    // Generate JWT token - FIXED: Use same payload structure as regular auth
    const token = jwt.sign(
      { id: user.id, email: user.email, name: user.name },
      process.env.JWT_SECRET || 'your-jwt-secret',
      { expiresIn: '24h' }
    );
    
    res.json({ token, user });
  } catch (error) {
    console.error('Mock login error:', error);
    res.status(500).json({ error: 'Mock login failed' });
  }
});

export default router;
