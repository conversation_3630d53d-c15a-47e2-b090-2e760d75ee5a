{"name": "jarvis-backend", "version": "1.0.0", "description": "Backend API for JARVIS AI Assistant with OpenAI integration", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build needed for Node.js'", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui"}, "dependencies": {"@openai/agents": "^0.0.11", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "express-rate-limit": "^7.2.0", "express-session": "^1.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "openai": "^4.52.7", "passport": "^0.7.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-microsoft": "^2.1.0", "sqlite3": "^5.1.7", "ws": "^8.18.3"}, "devDependencies": {"@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "msw": "^2.10.4", "node-mocks-http": "^1.17.2", "nodemon": "^3.0.2", "supertest": "^7.1.4", "vitest": "^3.2.4"}, "keywords": ["ai", "assistant", "openai", "jarvis"], "author": "JARVIS Team", "license": "MIT"}