# OAuth Setup Guide for JARVIS AI Assistant

## Overview
This guide will help you set up OAuth authentication with Google, GitHub, and Microsoft for your JARVIS AI Assistant.

## Step 1: Configure OAuth Providers

### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.developers.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials:
   - Application type: Web application
   - Authorized redirect URIs:
     - `http://localhost:5050/auth/google/callback` (development)
     - `https://your-backend-domain.com/auth/google/callback` (production)

### GitHub OAuth Setup
1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Create a new OAuth App:
   - Application name: "JARVIS AI Assistant"
   - Homepage URL: `http://localhost:5173` (development) or your production URL
   - Authorization callback URL:
     - `http://localhost:5050/auth/github/callback` (development)
     - `https://your-backend-domain.com/auth/github/callback` (production)

### Microsoft OAuth Setup
1. Go to [Azure Portal](https://portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationsListBlade)
2. Register a new application:
   - Redirect URIs:
     - `http://localhost:5050/auth/microsoft/callback` (development)
     - `https://your-backend-domain.com/auth/microsoft/callback` (production)
   - API permissions: Microsoft Graph → User.Read

## Step 2: Update Environment Variables

### Backend (.env in backend directory)
```bash
# Copy the example file
cp .env.example .env

# Add your OAuth credentials
echo "GOOGLE_CLIENT_ID=your_google_client_id" >> .env
echo "GOOGLE_CLIENT_SECRET=your_google_client_secret" >> .env
echo "GITHUB_CLIENT_ID=your_github_client_id" >> .env
echo "GITHUB_CLIENT_SECRET=your_github_client_secret" >> .env
echo "MICROSOFT_CLIENT_ID=your_microsoft_client_id" >> .env
echo "MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret" >> .env
echo "SESSION_SECRET=your_random_session_secret" >> .env
echo "JWT_SECRET=your_random_jwt_secret" >> .env
```

### Frontend (.env.local in frontend directory)
```bash
# Update API URLs to match your backend
echo "VITE_API_URL=http://localhost:5050" > .env.local
```

## Step 3: Database Setup

Run the SQL migration to create the users table:

```bash
# For local PostgreSQL
psql $DATABASE_URL -f backend/migrations/create_users_table.sql

# For Railway/Production
# The table will be created automatically on first user registration
```

## Step 4: Test the Setup

1. Start the backend:
```bash
cd backend
npm start
```

2. Start the frontend:
```bash
cd frontend
npm run dev
```

3. Visit `http://localhost:5173` and click "Login with Google/GitHub/Microsoft"

## Step 5: Production Deployment

### Railway Backend
Add these environment variables in Railway dashboard:
- `GOOGLE_CLIENT_ID`
- `GOOGLE_CLIENT_SECRET`
- `GITHUB_CLIENT_ID`
- `GITHUB_CLIENT_SECRET`
- `MICROSOFT_CLIENT_ID`
- `MICROSOFT_CLIENT_SECRET`
- `SESSION_SECRET` (generate random 32+ character string)
- `JWT_SECRET` (generate random 32+ character string)

### Netlify Frontend
Update the `VITE_API_URL` in your Netlify environment variables to point to your Railway backend URL.

## Troubleshooting

### Common Issues
1. **CORS errors**: Ensure your frontend URL is in the CORS whitelist
2. **OAuth redirect mismatch**: Check redirect URLs in OAuth provider settings
3. **Database connection**: Verify DATABASE_URL is set correctly
4. **Session issues**: Ensure SESSION_SECRET is set and consistent

### Testing OAuth Flow
1. Check browser console for any errors
2. Verify network requests to `/auth/*` endpoints
3. Check backend logs for authentication errors
4. Ensure all environment variables are loaded correctly
