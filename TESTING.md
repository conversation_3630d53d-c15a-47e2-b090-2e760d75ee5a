# JARVIS AI Assistant - Testing Guide

## Overview

This project uses **Vitest** as the primary testing framework for both frontend and backend components. Vitest provides excellent integration with Vite, fast execution, and Jest-compatible APIs.

## Test Commands

### Run All Tests
```bash
pnpm test                    # Run all tests (frontend + backend)
pnpm test:watch             # Run tests in watch mode
pnpm test:coverage          # Run tests with coverage report
pnpm test:ui                # Run tests with interactive UI
```

### Frontend-Specific Tests
```bash
pnpm test:frontend          # Run frontend tests only
pnpm test:frontend:watch    # Run frontend tests in watch mode
```

### Backend-Specific Tests
```bash
pnpm test:backend           # Run backend tests only
pnpm test:backend:watch     # Run backend tests in watch mode
```

## Testing Architecture

### Frontend Testing Stack
- **Vitest**: Test runner and framework
- **@testing-library/react**: React component testing utilities
- **@testing-library/jest-dom**: Custom Jest matchers for DOM
- **@testing-library/user-event**: User interaction simulation
- **jsdom**: DOM environment for testing
- **MSW (Mock Service Worker)**: API mocking
- **@vitest/coverage-v8**: Code coverage reporting

### Backend Testing Stack
- **Vitest**: Test runner and framework
- **Supertest**: HTTP assertion library
- **MSW**: External API mocking (OpenAI, etc.)
- **node-mocks-http**: HTTP request/response mocking
- **@vitest/coverage-v8**: Code coverage reporting

## Test Structure

### Frontend Tests
```
src/
├── test/
│   ├── setup.js              # Global test setup
│   └── mocks/
│       └── server.js         # MSW server setup
├── components/
│   └── __tests__/
│       └── JarvisAgent.test.jsx
├── hooks/
│   └── __tests__/
│       └── *.test.js
└── utils/
    └── __tests__/
        └── api.test.js
```

### Backend Tests
```
backend/
├── test/
│   ├── setup.js              # Global test setup
│   ├── mocks/
│   │   └── server.js         # MSW server setup
│   ├── routes/
│   │   └── server.test.js
│   ├── middleware/
│   │   └── *.test.js
│   └── utils/
│       └── *.test.js
```

## Mocking Strategy

### Browser APIs (Frontend)
- **Web Speech API**: Mocked for speech recognition and synthesis
- **File API**: Mocked for file upload testing
- **Fetch API**: Mocked for HTTP requests

### External APIs (Backend)
- **OpenAI API**: Mocked using MSW for consistent testing
- **WebSocket**: Mocked for realtime functionality testing

## Test Examples

### Component Testing
```javascript
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import JarvisAgent from '../JarvisAgent'

test('handles message sending', async () => {
  const user = userEvent.setup()
  render(<JarvisAgent />)
  
  const input = screen.getByPlaceholderText(/Type your message/i)
  await user.type(input, 'Hello JARVIS')
  await user.click(screen.getByRole('button', { name: /send/i }))
  
  expect(screen.getByText('Hello JARVIS')).toBeInTheDocument()
})
```

### API Testing
```javascript
import request from 'supertest'
import app from '../server'

test('POST /api/assistant/message', async () => {
  const response = await request(app)
    .post('/api/assistant/message')
    .send({ message: 'Hello' })
    .expect(200)
    
  expect(response.body.success).toBe(true)
})
```

## Coverage Reports

Coverage reports are generated in the `coverage/` directory for both frontend and backend:

- **HTML Report**: Open `coverage/index.html` in your browser
- **JSON Report**: Machine-readable coverage data
- **Text Report**: Console output during test runs

### Coverage Targets
- **Statements**: > 80%
- **Branches**: > 75%
- **Functions**: > 80%
- **Lines**: > 80%

## Continuous Integration

Tests are designed to run in CI/CD environments with:
- Headless browser support
- No external dependencies
- Deterministic test execution
- Proper cleanup and teardown

## Best Practices

### Writing Tests
1. **Arrange, Act, Assert**: Structure tests clearly
2. **Test Behavior**: Focus on what users see and do
3. **Mock External Dependencies**: Keep tests isolated
4. **Use Descriptive Names**: Make test intent clear
5. **Test Edge Cases**: Handle errors and edge conditions

### Component Testing
1. **Test User Interactions**: Click, type, drag, etc.
2. **Test Accessibility**: Screen readers, keyboard navigation
3. **Test Error States**: Loading, error, empty states
4. **Mock API Calls**: Use MSW for consistent responses

### API Testing
1. **Test All Endpoints**: Happy path and error cases
2. **Test Middleware**: Authentication, CORS, rate limiting
3. **Test Data Validation**: Input sanitization and validation
4. **Test Error Handling**: Proper error responses

## Debugging Tests

### Common Issues
1. **Async Operations**: Use `waitFor` for async updates
2. **Timer Issues**: Mock timers with `vi.useFakeTimers()`
3. **DOM Cleanup**: Tests should clean up after themselves
4. **Mock Persistence**: Clear mocks between tests

### Debug Commands
```bash
# Run specific test file
pnpm test JarvisAgent.test.jsx

# Run tests matching pattern
pnpm test --grep "message sending"

# Run tests with verbose output
pnpm test --reporter=verbose

# Run tests in debug mode
pnpm test --inspect-brk
```

## Performance Testing

While not included in this basic setup, consider adding:
- **Load Testing**: For API endpoints
- **Performance Testing**: For component rendering
- **Memory Leak Testing**: For long-running processes

## Security Testing

Tests include basic security validations:
- Input sanitization
- CORS configuration
- Rate limiting
- Authentication flows

For production, consider adding:
- **Penetration Testing**
- **Dependency Vulnerability Scanning**
- **Security Headers Validation**
