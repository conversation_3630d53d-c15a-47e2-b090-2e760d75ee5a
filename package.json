{"name": "jarvis-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start": "cd backend && pnpm install && node server.js", "test": "vitest run && cd backend && vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage && cd backend && vitest run --coverage", "test:ui": "vitest --ui", "test:frontend": "vitest run", "test:backend": "cd backend && vitest run", "test:frontend:watch": "vitest", "test:backend:watch": "cd backend && vitest"}, "dependencies": {"lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-router-dom": "^6.30.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "msw": "^2.10.4", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "vite": "^4.4.5", "vitest": "^3.2.4"}}