{"extends": ["eslint:recommended", "@eslint/js/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"ecmaVersion": 2021, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"browser": true, "es2021": true, "node": true}, "plugins": ["react", "react-hooks"], "rules": {"react/prop-types": "off", "react/react-in-jsx-scope": "off", "no-unused-vars": "warn"}, "settings": {"react": {"version": "detect"}}}