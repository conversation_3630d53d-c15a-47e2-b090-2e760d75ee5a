#!/bin/bash

# JARVIS AI Assistant - Testing Setup Script
# This script installs and configures Vitest for both frontend and backend testing

set -e

echo "🧪 Setting up comprehensive testing framework for JARVIS AI Assistant..."
echo "=================================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm is not installed. Please install pnpm first:"
    echo "npm install -g pnpm"
    exit 1
fi

print_status "Installing frontend testing dependencies..."

# Install frontend testing dependencies
pnpm add -D \
    vitest \
    @vitest/ui \
    @testing-library/react \
    @testing-library/jest-dom \
    @testing-library/user-event \
    jsdom \
    @vitest/coverage-v8 \
    msw \
    happy-dom

print_success "Frontend testing dependencies installed!"

print_status "Installing backend testing dependencies..."

# Install backend testing dependencies
cd backend
pnpm add -D \
    vitest \
    @vitest/ui \
    @vitest/coverage-v8 \
    supertest \
    msw \
    node-mocks-http

cd ..

print_success "Backend testing dependencies installed!"

print_status "Creating test configuration files..."

# Create vitest config for frontend
cat > vitest.config.js << 'EOF'
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.js'],
    css: true,
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.config.js',
        'dist/',
        'coverage/',
      ],
    },
  },
})
EOF

# Create vitest config for backend
cat > backend/vitest.config.js << 'EOF'
import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./test/setup.js'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'test/',
        '**/*.config.js',
        'coverage/',
      ],
    },
  },
})
EOF

print_success "Vitest configuration files created!"

print_status "Creating test directories and setup files..."

# Create frontend test directories
mkdir -p src/test
mkdir -p src/components/__tests__
mkdir -p src/hooks/__tests__
mkdir -p src/utils/__tests__

# Create backend test directories
mkdir -p backend/test
mkdir -p backend/test/routes
mkdir -p backend/test/middleware
mkdir -p backend/test/utils

print_success "Test directories created!"

print_status "Test setup completed successfully! 🎉"
echo ""
echo "📋 Available test commands:"
echo "  pnpm test              - Run all tests"
echo "  pnpm test:watch        - Run tests in watch mode"
echo "  pnpm test:coverage     - Run tests with coverage"
echo "  pnpm test:ui           - Run tests with UI"
echo "  pnpm test:frontend     - Run frontend tests only"
echo "  pnpm test:backend      - Run backend tests only"
echo ""
echo "🚀 Next steps:"
echo "  1. Run 'pnpm test' to verify the setup"
echo "  2. Check the example test files created"
echo "  3. Start writing your own tests!"
echo ""
print_success "Happy testing! 🧪✨"
